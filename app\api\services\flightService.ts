// import axios from 'axios';

// // Create a separate axios instance for flight service with different base URL
// const flightApi = axios.create({
//   baseURL: 'http://*************:8080/api/v1',
//   headers: {
//     'Content-Type': 'application/json',
//   },
//   timeout: 20000, // 20 seconds timeout for flight searches
// });
// import { AirportSelectorData, airportService } from './airportService';

// // Flight Search Request Models
// export interface FlightSearchTrip {
//   From: string;
//   To: string;
//   OnwardDate: string;
//   ReturnDate?: string;
//   TUI?: string;
// }

// export interface FlightSearchParameters {
//   IsDirect: boolean;
//   PaxCategory: string;
//   Refundable: string;
// }

// export interface FlightSearchRequest {
//   ADT: number;
//   CHD: number;
//   INF: number;
//   Cabin: string;
//   Source: string;
//   Mode: string;
//   ClientID: string;
//   FareType: string;
//   SecType: string;
//   TUI: string;
//   Trips: FlightSearchTrip[];
//   Parameters: FlightSearchParameters;
// }

// // Flight Search Response Models
// export interface FlightSegment {
//   airline: string;
//   flightNumber: string;
//   departure: {
//     time: string;
//     airport: string;
//     city: string;
//     date: string;
//     terminal?: string;
//   };
//   arrival: {
//     time: string;
//     airport: string;
//     city: string;
//     date: string;
//     terminal?: string;
//   };
//   duration: string;
//   stops: string;
//   aircraft?: string;
//   operatingAirline?: string;
// }

// // Search List API Response Models
// export interface SearchListConnection {
//   Airport: string;
//   ArrAirportName: string;
//   Duration: string;
//   Type: string;
//   MAC: string;
// }

// export interface SearchListJourney {
//   Stops: number;
//   Seats: number;
//   ReturnIdentifier: number;
//   Index: string;
//   Provider: string;
//   FlightNo: string;
//   ArrivalTerminal: string | null;
//   DepartureTerminal: string | null;
//   VAC: string;
//   MAC: string;
//   OAC: string;
//   ArrivalTime: string;
//   DepartureTime: string;
//   FareClass: string | null;
//   Duration: string;
//   GroupCount: number;
//   TotalFare: string;
//   GrossFare: number;
//   TotalCommission: number;
//   NetFare: number;
//   Hops: number;
//   Notice: string;
//   NoticeLink: string | null;
//   NoticeType: string;
//   Refundable: string;
//   Alliances: string | null;
//   Amenities: string | null;
//   Hold: boolean;
//   Connections: SearchListConnection[];
//   From: string;
//   To: string;
//   FromName: string;
//   ToName: string;
//   AirlineName: string;
//   AirCraft: string;
//   RBD: string;
//   Cabin: string;
//   FBC: string;
//   FCBegin: string | null;
//   FCEnd: string | null;
//   FCType: string;
//   GFL: boolean;
//   Promo: string;
//   Recommended: boolean;
//   Premium: boolean;
//   JourneyKey: string;
//   FareKey: string;
//   PaxCategory: string;
//   PrivateFareType: string;
//   DealKey: string;
//   VACAirlineLogo: string;
//   MACAirlineLogo: string;
//   OACAirlineLogo: string;
// }

// export interface SearchListTrip {
//   Journey: SearchListJourney[];
// }

// export interface SearchListResponse {
//   TUI: string;
//   Completed: string;
//   CeilingInfo: string;
//   TripType: string | null;
//   ElapsedTime: string;
//   Notices: string | null;
//   Code?: string; // Optional - not present in searchlistdummy.json
//   Msg?: string[]; // Optional - not present in searchlistdummy.json
//   Trips: SearchListTrip[];
// }

// export interface SearchListRequest {
//   TUI: string;
// }

// // Actual Search API Response (what the API actually returns)
// export interface ActualSearchApiResponse {
//   Code: string;
//   TUI: string;
//   is_completed: string[];
// }

// export interface FlightFare {
//   fareType: string;
//   price: number;
//   originalPrice?: number;
//   currency: string;
//   baggage: {
//     cabin: string;
//     checkin: string;
//   };
//   cancellation: string;
//   flexibility?: {
//     cancellation: string;
//     dateChange: string;
//   };
//   meals?: string;
//   seats?: string;
//   benefits?: {
//     title: string;
//     items: string[];
//   };
// }

// // Enhanced interfaces for detailed flight information
// export interface FlightAircraft {
//   type: string;
//   configuration: string;
//   totalSeats: number;
//   amenities: string[];
// }

// export interface FlightRoute {
//   distance: string;
//   flightPath: string;
//   averageFlightTime: string;
// }

// export interface FlightMeals {
//   available: boolean;
//   type: string;
//   specialMeals: string[];
// }

// export interface FlightEntertainment {
//   available: boolean;
//   type: string;
//   wifi: boolean;
// }

// export interface FlightAccessibility {
//   wheelchairAccessible: boolean;
//   assistanceAvailable: boolean;
//   specialNeeds: string;
// }

// export interface FlightServices {
//   meals: FlightMeals;
//   entertainment: FlightEntertainment;
//   accessibility: FlightAccessibility;
// }

// export interface FlightCheckinTime {
//   domestic: string;
//   webCheckin: string;
// }

// export interface FlightBaggagePolicy {
//   cabin: string;
//   checkin: string;
//   excessBaggage: string;
// }

// export interface FlightCancellationPolicy {
//   saver: string;
//   flexi: string;
//   business: string;
// }

// export interface FlightPolicies {
//   checkinTime: FlightCheckinTime;
//   baggagePolicy: FlightBaggagePolicy;
//   cancellation: FlightCancellationPolicy;
// }

// export interface FlightDetailedInfo {
//   aircraft: FlightAircraft;
//   route: FlightRoute;
//   services: FlightServices;
//   policies: FlightPolicies;
// }

// export interface Flight {
//   id: string;
//   airline: string;
//   logo: string;
//   flightNumber: string;
//   segments: FlightSegment[];
//   departure: {
//     time: string;
//     airport: string;
//     city: string;
//     date: string;
//     terminal?: string;
//   };
//   arrival: {
//     time: string;
//     airport: string;
//     city: string;
//     date: string;
//     terminal?: string;
//   };
//   duration: string;
//   stops: string;
//   price: number;
//   originalPrice?: number;
//   currency: string;
//   baggage: {
//     cabin: string;
//     checkin: string;
//   };
//   cancellation: string;
//   rating?: number;
//   features: string[];
//   fares: FlightFare[];
//   isRefundable: boolean;
//   fareType: string;
//   detailedInfo?: FlightDetailedInfo; // Optional detailed information for flight details page
// }

// // Flight Details API Response
// export interface FlightDetailsResponse {
//   success: boolean;
//   message?: string;
//   data: Flight;
// }

// export interface FlightSearchResponse {
//   success: boolean;
//   message?: string;
//   data: {
//     onwardFlights: Flight[];
//     returnFlights?: Flight[];
//     searchId: string;
//     totalResults: number;
//     tui?: string; // TUI from search response for search-list API
//     filters: {
//       airlines: string[];
//       priceRange: { min: number; max: number };
//       durationRange: { min: number; max: number };
//       stops: string[];
//       departureTimeSlots: string[];
//       arrivalTimeSlots: string[];
//     };
//   };
//   error?: string;
// }

// // Helper interface for frontend flight search form
// export interface FlightSearchFormData {
//   from: AirportSelectorData;
//   to: AirportSelectorData;
//   departureDate: string;
//   returnDate?: string;
//   adults: number;
//   children: number;
//   infants: number;
//   cabin: 'E' | 'PE' | 'B' | 'F'; // Economy, Premium Economy, Business, First
//   tripType: 'oneWay' | 'roundTrip';
//   fareType: 'ON' | 'OFF'; // Online/Offline
//   isDirect: boolean;
// }

// // Utility function to determine if flight is domestic or international
// export const isFlightDomestic = async (fromCode: string, toCode: string): Promise<boolean> => {
//   try {
//     const fromAirport = await airportService.getAirportByCode(fromCode);
//     const toAirport = await airportService.getAirportByCode(toCode);

//     if (!fromAirport || !toAirport) {
//       throw new Error('Airport not found');
//     }

//     // Check if both airports are in the same country
//     return fromAirport.country === toAirport.country;
//   } catch (error) {
//     console.error('Error determining flight type:', error);
//     throw error;
//   }
// };

// // Helper function to transform SearchListJourney to Flight
// export const transformSearchListJourneyToFlight = (journey: SearchListJourney): Flight => {
//   try {

//     // Parse departure and arrival times with error handling
//     let departureTime: Date;
//     let arrivalTime: Date;

//     try {
//       // Handle both ISO format (2025-08-14T07:35:00) and simple format
//       const depTime = journey.DepartureTime;
//       const arrTime = journey.ArrivalTime;

//       departureTime = new Date(depTime);
//       arrivalTime = new Date(arrTime);

//       // Validate dates
//       if (isNaN(departureTime.getTime()) || isNaN(arrivalTime.getTime())) {
//         throw new Error('Invalid date format');
//       }
//     } catch (dateError) {
//       console.warn('Date parsing failed, using fallback dates:', dateError);
//       departureTime = new Date();
//       arrivalTime = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours later
//     }

//     // Create segments from the journey
//     const segments: FlightSegment[] = [];

//     // Main segment
//     segments.push({
//       airline: journey.MAC || 'AI',
//       flightNumber: journey.FlightNo || '0000',
//       departure: {
//         time: departureTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }),
//         airport: journey.From || 'DEL',
//         city: (journey.FromName || 'Delhi').split('|')[1] || (journey.FromName || 'Delhi').split('|')[0] || 'Delhi',
//         date: departureTime.toISOString().split('T')[0],
//         terminal: journey.DepartureTerminal || undefined
//       },
//       arrival: {
//         time: arrivalTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }),
//         airport: journey.To || 'BOM',
//         city: (journey.ToName || 'Mumbai').split('|')[1] || (journey.ToName || 'Mumbai').split('|')[0] || 'Mumbai',
//         date: arrivalTime.toISOString().split('T')[0],
//         terminal: journey.ArrivalTerminal || undefined
//       },
//       duration: journey.Duration || '2h 00m',
//       stops: (journey.Stops || 0).toString(),
//       aircraft: journey.AirCraft || 'Aircraft',
//       operatingAirline: (journey.AirlineName || 'Airline').split('|')[0] || journey.AirlineName || 'Airline'
//     });

//   // Add connection segments if any
//   if (journey.Connections && Array.isArray(journey.Connections)) {
//     journey.Connections.forEach((connection, index) => {
//       segments.push({
//         airline: connection.MAC?.split('|')[0] || connection.MAC || 'Unknown',
//         flightNumber: `Connection ${index + 1}`,
//         departure: {
//           time: '',
//           airport: connection.Airport || '',
//           city: connection.ArrAirportName?.split('|')[1] || connection.ArrAirportName || '',
//           date: '',
//         },
//         arrival: {
//           time: '',
//           airport: '',
//           city: '',
//           date: '',
//         },
//         duration: connection.Duration || '',
//         stops: '0',
//         operatingAirline: connection.MAC?.split('|')[1] || connection.MAC || 'Unknown'
//       });
//     });
//   }

//   return {
//     id: typeof journey.Index === 'string' ? journey.Index : String(journey.Index || 'unknown'),
//     airline: journey.MAC,
//     logo: journey.VACAirlineLogo?.replace(/\\/g, '/').replace('/Content/Templates/images/', '/') || '/AirlineLogo/default.png',
//     flightNumber: journey.FlightNo,
//     segments,
//     departure: {
//       time: departureTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }),
//       airport: journey.From,
//       city: journey.FromName?.split('|')[1] || journey.FromName || 'Delhi',
//       date: departureTime.toISOString().split('T')[0],
//       terminal: journey.DepartureTerminal || undefined
//     },
//     arrival: {
//       time: arrivalTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }),
//       airport: journey.To,
//       city: journey.ToName?.split('|')[1] || journey.ToName || 'Mumbai',
//       date: arrivalTime.toISOString().split('T')[0],
//       terminal: journey.ArrivalTerminal || undefined
//     },
//     duration: journey.Duration,
//     stops: (journey.Stops || 0).toString(),
//     price: journey.NetFare || 0, // Price after discount (what customer pays)
//     originalPrice: journey.GrossFare || 0, // Original price before discount
//     // Note: TotalCommission (${journey.TotalCommission}) represents the discount amount: GrossFare - NetFare
//     currency: 'INR', // Assuming INR based on the fare key format
//     baggage: {
//       cabin: '7 kg',
//       checkin: '15 kg'
//     },
//     cancellation: journey.Refundable === 'R' ? 'Refundable' : 'Non-refundable',
//     rating: 4.2, // Default rating
//     features: journey.Amenities?.split(',') || ['Meal', 'Entertainment'],
//     fares: [{
//       fareType: journey.FareClass || journey.Cabin,
//       price: journey.NetFare || 0, // Price after discount
//       originalPrice: journey.GrossFare || 0, // Original price before discount
//       currency: 'INR',
//       baggage: {
//         cabin: '7 kg',
//         checkin: '15 kg'
//       },
//       cancellation: journey.Refundable === 'R' ? 'Refundable' : 'Non-refundable',
//       benefits: {
//         title: journey.Refundable === 'R' ? 'Refundable Fare' : 'Non-refundable Fare',
//         items: journey.Refundable === 'R' ? ['Full refund available', 'Cancellation charges may apply'] : ['Non-refundable fare', 'No refund on cancellation']
//       }
//     }],
//     isRefundable: journey.Refundable === 'R',
//     fareType: journey.FareClass || journey.Cabin
//   };
//   } catch (error) {
//     console.error('Error transforming journey to flight:', error, journey);

//     // Return a fallback flight object if transformation fails
//     return {
//       id: journey.Index || 'fallback-id',
//       airline: journey.MAC || 'AI',
//       logo: '/images/default-airline.png',
//       flightNumber: journey.FlightNo || '0000',
//       segments: [],
//       departure: {
//         time: '06:00',
//         airport: journey.From || 'DEL',
//         city: 'Delhi',
//         date: new Date().toISOString().split('T')[0]
//       },
//       arrival: {
//         time: '08:00',
//         airport: journey.To || 'BOM',
//         city: 'Mumbai',
//         date: new Date().toISOString().split('T')[0]
//       },
//       duration: journey.Duration || '2h 00m',
//       stops: (journey.Stops || 0).toString(),
//       price: journey.NetFare || 5000, // Price after discount
//       currency: 'INR',
//       baggage: {
//         cabin: '7 kg',
//         checkin: '15 kg'
//       },
//       cancellation: 'Non-refundable',
//       rating: 4.0,
//       features: ['Meal'],
//       fares: [{
//         fareType: 'Economy',
//         price: journey.NetFare || 5000, // Price after discount
//         currency: 'INR',
//         baggage: {
//           cabin: '7 kg',
//           checkin: '15 kg'
//         },
//         cancellation: 'Non-refundable'
//       }],
//       isRefundable: false,
//       fareType: 'Economy'
//     };
//   }
// };





// // Helper function to load dummy search-list data - this represents the exact same response structure as the real API
// export const loadDummySearchListData = async (): Promise<SearchListResponse> => {
//   try {

//     // In a browser environment, we need to fetch the JSON file
//     const response = await fetch('/assets/json/dummydata/searchlistdummy.json');

//     if (!response.ok) {
//       throw new Error(`Failed to load dummy data: ${response.status} ${response.statusText}`);
//     }

//     const dummyData: SearchListResponse = await response.json();

//     return dummyData;
//   } catch (error) {
//     console.error('Error loading dummy search-list data:', error);

//     // Return a minimal fallback response if file loading fails
//     return {
//       TUI: "DUMMY_TUI",
//       Completed: "True",
//       CeilingInfo: "R0",
//       TripType: null,
//       ElapsedTime: "0",
//       Notices: null,
//       Trips: []
//     };
//   }
// };

// // Preview Data Types - Updated to match new JSON structure
// export interface PreviewFlightSegment {
//   Flight: {
//     FUID: number;
//     VAC: string;
//     MAC: string;
//     OAC: string;
//     FlightNo: string;
//     ArrivalTime: string;
//     DepartureTime: string;
//     FareClass: string;
//     ArrivalCode: string;
//     DepartureCode: string;
//     ArrivalTerminal: string;
//     DepartureTerminal: string;
//     Duration: string;
//     AirCraft: string;
//     Refundable: string;
//     Seats: number;
//   };
//   Fares: {
//     GrossFare: number;
//     NetFare: number;
//     TotalBaseFare: number;
//     TotalTax: number;
//     TotalCommission: number;
//   };
// }

// export interface PreviewJourney {
//   Provider: string;
//   Stops: string;
//   GrossFare: number;
//   NetFare: number;
//   Duration: string;
//   Segments: PreviewFlightSegment[];
// }

// export interface PreviewTrip {
//   Journey: PreviewJourney[];
// }

// export interface PreviewData {
//   TUI: string;
//   Code: string;
//   Msg: string[];
//   From: string;
//   To: string;
//   FromName: string;
//   ToName: string;
//   OnwardDate: string;
//   ReturnDate: string;
//   NetAmount: number;
//   GrossAmount: number;
//   InsPremium: number;
//   FareType: string;
//   Trips: PreviewTrip[];
//   Rules: any[];
//   SSR: any[];
//   INSFare: any[];
// }

// export interface PreviewResponse {
//   success: boolean;
//   message: string;
//   data: PreviewData | null;
// }

// // Helper function to load dummy preview data - updated for new JSON structure
// export const loadDummyPreviewData = async (): Promise<PreviewData> => {
//   try {

//     // In a browser environment, we need to fetch the JSON file
//     const response = await fetch('/assets/json/dummydata/previewdummy.json');

//     if (!response.ok) {
//       throw new Error(`Failed to load preview dummy data: ${response.status} ${response.statusText}`);
//     }

//     const dummyData: PreviewData = await response.json();

//     return dummyData;
//   } catch (error) {
//     console.error('Error loading dummy preview data:', error);

//     // Return a minimal fallback response if file loading fails
//     return {
//       TUI: "FALLBACK_TUI",
//       Code: "200",
//       Msg: ["Fallback data"],
//       From: "BOM",
//       To: "DEL",
//       FromName: "Mumbai",
//       ToName: "Delhi",
//       OnwardDate: new Date().toISOString().split('T')[0],
//       ReturnDate: "",
//       NetAmount: 4500,
//       GrossAmount: 4600,
//       InsPremium: 199,
//       FareType: "ON",
//       Trips: [],
//       Rules: [],
//       SSR: [],
//       INSFare: []
//     };
//   }
// };

// // Helper function to load flight details dummy data
// export const loadDummyFlightDetailsData = async (): Promise<FlightDetailsResponse> => {
//   try {

//     // In a browser environment, we need to fetch the JSON file
//     const response = await fetch('/assets/json/dummydata/flightdetails.json');

//     if (!response.ok) {
//       throw new Error(`Failed to load flight details dummy data: ${response.status} ${response.statusText}`);
//     }

//     const dummyData: FlightDetailsResponse = await response.json();

//     return dummyData;
//   } catch (error) {
//     console.error('❌ Error loading flight details dummy data:', error);

//     // Return a minimal fallback response if file loading fails
//     return {
//       success: false,
//       message: 'Fallback data - JSON file not available',
//       data: {
//         id: 'fallback-flight',
//         airline: '6E',
//         logo: '/AirlineLogo/6E.png',
//         flightNumber: '6E-XXXX',
//         segments: [],
//         departure: {
//           time: '06:00',
//           airport: 'DEL',
//           city: 'Delhi',
//           date: new Date().toISOString().split('T')[0]
//         },
//         arrival: {
//           time: '09:00',
//           airport: 'BOM',
//           city: 'Mumbai',
//           date: new Date().toISOString().split('T')[0]
//         },
//         duration: '3h 00m',
//         stops: 'Non-stop',
//         price: 5000,
//         currency: 'INR',
//         baggage: {
//           cabin: '7 kg',
//           checkin: '15 kg'
//         },
//         cancellation: 'Standard cancellation policy',
//         features: [],
//         fares: [],
//         isRefundable: false,
//         fareType: 'SAVER'
//       }
//     };
//   }
// };

// // Helper function to transform SearchListResponse to Flight array
// export const transformSearchListResponseToFlights = (searchListResponse: SearchListResponse): Flight[] => {
//   try {

//     const flights: Flight[] = [];

//     if (!searchListResponse.Trips || searchListResponse.Trips.length === 0) {
//       console.warn('⚠️ No trips found in SearchListResponse');
//       return flights;
//     }

//     searchListResponse.Trips.forEach((trip, tripIndex) => {

//       if (!trip.Journey || trip.Journey.length === 0) {
//         console.warn(`Trip ${tripIndex + 1} has no journeys`);
//         return;
//       }

//       trip.Journey.forEach((journey, journeyIndex) => {
//         try {
//           const flight = transformSearchListJourneyToFlight(journey);
//           flights.push(flight);
//         } catch (error) {
//           console.error(`Failed to transform journey ${journeyIndex + 1}:`, error, journey);
//         }
//       });
//     });

//     return flights;
//   } catch (error) {
//     console.error('❌ Error transforming SearchListResponse to flights:', error);
//     return [];
//   }
// };

// // Transform frontend form data to API request format
// export const transformToFlightSearchRequest = async (
//   formData: FlightSearchFormData
// ): Promise<FlightSearchRequest> => {
//   const isDomestic = await isFlightDomestic(formData.from.code, formData.to.code);
  
//   const trip: FlightSearchTrip = {
//     From: formData.from.code,
//     To: formData.to.code,
//     OnwardDate: formData.departureDate,
//     ReturnDate: formData.tripType === 'roundTrip' ? formData.returnDate || '' : '',
//     TUI: ''
//   };

//   return {
//     ADT: formData.adults,
//     CHD: formData.children,
//     INF: formData.infants,
//     Cabin: formData.cabin,
//     Source: 'CF',
//     Mode: 'AS',
//     ClientID: '',
//     FareType: formData.fareType,
//     SecType: isDomestic ? 'D' : 'I', // D for Domestic, I for International
//     TUI: '',
//     Trips: [trip],
//     Parameters: {
//       IsDirect: formData.isDirect,
//       PaxCategory: 'NA',
//       Refundable: ''
//     }
//   };
// };

// // Flight Search Service
// export const flightService = {
//   // Search flights
//   async searchFlights(searchRequest: FlightSearchRequest): Promise<FlightSearchResponse> {

//     try {
//       // Try to call the real API first
//       const response = await flightApi.post<ActualSearchApiResponse>('/search', searchRequest);

//       if (response.data) {

//         // Transform the actual API response to our expected format
//         if (response.data.Code === "200" && response.data.TUI) {

//           // Create a minimal response with the TUI for search-list polling
//           const transformedResponse: FlightSearchResponse = {
//             success: true,
//             message: 'Search completed, TUI received for search-list API polling',
//             data: {
//               onwardFlights: [],
//               returnFlights: undefined,
//               searchId: response.data.TUI,
//               totalResults: 0,
//               tui: response.data.TUI, 
//               filters: {
//                 airlines: [],
//                 priceRange: { min: 0, max: 100000 },
//                 durationRange: { min: 0, max: 1440 },
//                 stops: [],
//                 departureTimeSlots: [],
//                 arrivalTimeSlots: []
//               }
//             }
//           };

//           return transformedResponse;
//         }
//       }
//     } catch (apiError) {
//       console.warn('⚠️ Real API failed, falling back to dummy data:', apiError);
//     }

//     // Fallback: Load dummy data and transform to expected response format
//     const dummySearchListData = await loadDummySearchListData();
//     const dummyFlights = transformSearchListResponseToFlights(dummySearchListData);

//     const dummyResponse: FlightSearchResponse = {
//       success: true,
//       message: 'Using dummy data - exact API response format',
//       data: {
//         onwardFlights: dummyFlights,
//         returnFlights: undefined,
//         searchId: 'dummy-search-id',
//         totalResults: dummyFlights.length,
//         tui: dummySearchListData.TUI,
//         filters: {
//           airlines: [...new Set(dummyFlights.map(f => f.airline))],
//           priceRange: {
//             min: Math.min(...dummyFlights.map(f => f.price)),
//             max: Math.max(...dummyFlights.map(f => f.price))
//           },
//           durationRange: { min: 120, max: 300 },
//           stops: [...new Set(dummyFlights.map(f => f.stops))],
//           departureTimeSlots: ['morning', 'afternoon', 'evening'],
//           arrivalTimeSlots: ['morning', 'afternoon', 'evening']
//         }
//       }
//     };

//     return dummyResponse;
//   },

//   // Search flights with form data (convenience method)
//   async searchFlightsWithFormData(formData: FlightSearchFormData): Promise<FlightSearchResponse> {
//     try {
//       const searchRequest = await transformToFlightSearchRequest(formData);
//       return await this.searchFlights(searchRequest);
//     } catch (error) {
//       console.error('Error searching flights with form data:', error);
//       throw error;
//     }
//   },

//   // Enhanced search flights with form data and direct search-list call
//   async searchFlightsWithFormDataAndSearchList(formData: FlightSearchFormData): Promise<{ searchResponse: FlightSearchResponse; searchListResponse?: SearchListResponse }> {
//     try {
//       const searchRequest = await transformToFlightSearchRequest(formData);
      
//       // Call search-list directly with the new format
//       const searchListResponse = await this.searchListDirect(searchRequest);
      
//       // Transform the search-list response to flights
//       const flights = transformSearchListResponseToFlights(searchListResponse);
      
//       // Create a search response from the search-list data
//       const searchResponse: FlightSearchResponse = {
//         success: true,
//         message: 'Direct search-list call completed successfully',
//         data: {
//           onwardFlights: flights,
//           returnFlights: formData.tripType === 'roundTrip' ? flights : undefined,
//           searchId: searchListResponse.TUI || 'direct-search-id',
//           totalResults: flights.length,
//           tui: searchListResponse.TUI,
//           filters: {
//             airlines: [...new Set(flights.map(f => f.airline))],
//             priceRange: {
//               min: Math.min(...flights.map(f => f.price)),
//               max: Math.max(...flights.map(f => f.price))
//             },
//             durationRange: { min: 120, max: 300 },
//             stops: [...new Set(flights.map(f => f.stops))],
//             departureTimeSlots: ['morning', 'afternoon', 'evening'],
//             arrivalTimeSlots: ['morning', 'afternoon', 'evening']
//           }
//         }
//       };
      
//       return { searchResponse, searchListResponse };
//     } catch (error) {
//       console.error('Error in direct search-list call, falling back to dummy data:', error);

//       // Fallback to dummy data if direct call fails
//       try {
//         const dummySearchListData = await loadDummySearchListData();
//         const dummyFlights = transformSearchListResponseToFlights(dummySearchListData);

//         const fallbackSearchResponse: FlightSearchResponse = {
//           success: true,
//           message: 'Using dummy data due to direct search-list failure',
//           data: {
//             onwardFlights: dummyFlights,
//             returnFlights: formData.tripType === 'roundTrip' ? dummyFlights : undefined,
//             searchId: 'fallback-search-id',
//             totalResults: dummyFlights.length,
//             tui: dummySearchListData.TUI,
//             filters: {
//               airlines: [...new Set(dummyFlights.map(f => f.airline))],
//               priceRange: {
//                 min: Math.min(...dummyFlights.map(f => f.price)),
//                 max: Math.max(...dummyFlights.map(f => f.price))
//               },
//               durationRange: { min: 120, max: 300 },
//               stops: [...new Set(dummyFlights.map(f => f.stops))],
//               departureTimeSlots: ['morning', 'afternoon', 'evening'],
//               arrivalTimeSlots: ['morning', 'afternoon', 'evening']
//             }
//           }
//         };

//         return { searchResponse: fallbackSearchResponse, searchListResponse: dummySearchListData };
//       } catch (fallbackError) {
//         console.error('Fallback also failed:', fallbackError);
//         throw error;
//       }
//     }
//   },

//   // Get flight details by ID with API-first approach and fallback to mock data
//   async getFlightDetails(flightId: string): Promise<FlightDetailsResponse> {

//     try {
//       // API-first approach: Try API first, fallback to JSON automatically
//       // Set to true only if you want to force JSON loading for testing
//       const FORCE_FLIGHT_DETAILS_DUMMY_DATA_FOR_TESTING = false; // Set to false for API-first approach

//       if (FORCE_FLIGHT_DETAILS_DUMMY_DATA_FOR_TESTING) {
//         throw new Error('Forcing JSON fallback for testing');
//       }

//       // Use the v1 API structure
//       const apiUrl = `/api/v1/flights/${flightId}`;

//       // Call the actual flight details API through our Next.js API route
//       const apiResponse = await fetch(apiUrl, {
//         method: 'GET',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       if (!apiResponse.ok) {
//         throw new Error(`Flight details API returned ${apiResponse.status}: ${apiResponse.statusText}`);
//       }

//       const response: FlightDetailsResponse = await apiResponse.json();

//       if (response && response.success && response.data) {

//         return response;
//       } else {
//         throw new Error('Invalid response format from flight details API');
//       }

//     } catch (apiError) {
//       console.warn('⚠️ Flight details API failed, falling back to dummy data:', apiError);

//       // Fallback: Load dummy data from JSON file
//       const dummyFlightDetailsData = await loadDummyFlightDetailsData();

//       // Customize the dummy data with the requested flight ID if needed
//       if (dummyFlightDetailsData.success && dummyFlightDetailsData.data) {
//         dummyFlightDetailsData.data.id = flightId;
//         dummyFlightDetailsData.message = 'Flight details loaded from flightdetails.json file (API not available)';
//       }

//       const flightDetailsResponse: FlightDetailsResponse = {
//         success: true,
//         message: 'Flight details loaded from flightdetails.json file (API not available)',
//         data: dummyFlightDetailsData.data
//       };

//       return flightDetailsResponse;
//     }
//   },

//   // Legacy method for backward compatibility - returns just the Flight object
//   async getFlightDetailsLegacy(flightId: string): Promise<Flight> {
//     const response = await this.getFlightDetails(flightId);
//     return response.data;
//   },

//   // Get flight fare rules
//   async getFlightFareRules(flightId: string, fareType: string): Promise<any> {
//     try {

//       const response = await flightApi.get(`/flights/${flightId}/fare-rules?fareType=${fareType}`);

//       return response.data;
//     } catch (error) {
//       console.error('Error getting fare rules:', error);
//       throw error;
//     }
//   },

//   // Check flight availability
//   async checkFlightAvailability(flightId: string): Promise<boolean> {
//     try {

//       const response = await flightApi.get<{ success: boolean; available: boolean }>(`/flights/${flightId}/availability`);

//       if (!response.data || !response.data.success) {
//         throw new Error('Invalid response format from flight availability API');
//       }

//       return response.data.available;
//     } catch (error) {
//       console.error('Error checking flight availability:', error);
//       throw error;
//     }
//   },

//   // Direct Search List API - Call with new format structure
//   async searchListDirect(searchRequest: FlightSearchRequest): Promise<SearchListResponse> {
    
//     // Generate a TUI for the search
//     const tui = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-domestic_search1`;
    
//     // Create the new format payload
//     const payload = {
//       tui: tui,
//       search: searchRequest
//     };
    
    
//     try {
//       // Call the search-list API directly with the new format
//       const response = await flightApi.post<SearchListResponse>('/search', payload);
      
//       if (response.data) {
//         return response.data;
//       } else {
//         throw new Error('No data received from search-list API');
//       }
//     } catch (apiError) {
//       console.error('❌ Direct search-list API failed:', apiError);
      
//       // Fallback to dummy data
//       const dummyData = await loadDummySearchListData();
//       return dummyData;
//     }
//   },

//   // Search List API - Call with TUI and poll until completed, with fallback to dummy data
//   async searchList(tui: string, timeoutSeconds: number = 30): Promise<SearchListResponse> {

//     try {
//       // Try to call the real API first
//       const startTime = Date.now();
//       const timeoutMs = timeoutSeconds * 1000;
//       let callCount = 0;
//       const maxCalls = 30; // Increased from 5 to 30 calls

//       while (callCount < maxCalls) {
//         callCount++;

//         try {
//           const response = await flightApi.get<SearchListResponse>(`/search/${tui}`);

//           if (response.data) {

//             // Check if completed - handle both possible completion field formats
//             const isCompleted = response.data.Completed === 'True' ||
//                                response.data.Completed === 'true' ||
//                                (response.data as any).is_completed?.includes('success');


//             if (isCompleted) {
//               return response.data;
//             }

//             // Check timeout
//             const elapsedTime = Date.now() - startTime;
//             if (elapsedTime >= timeoutMs) {
//               break;
//             }

//             // Wait before next poll
//             await new Promise(resolve => setTimeout(resolve, 1000));
//           }
//         } catch (pollError) {
//           break;
//         }
//       }
//     } catch (apiError) {
//       console.warn('⚠️ Search-list API polling failed, falling back to dummy data:', apiError);
//     }

//     // Fallback: Return your exact searchlistdummy.json data
//     return await loadDummySearchListData();
//   },

//   // Enhanced search flights with search-list integration and fallback
//   async searchFlightsWithSearchList(searchRequest: FlightSearchRequest): Promise<{ searchResponse: FlightSearchResponse; searchListResponse?: SearchListResponse }> {
//     try {

//       // First, call the regular search API
//       let searchResponse: FlightSearchResponse;

//       try {
//         // For testing purposes, you can force the fallback to dummy data
//         // Set this to true to test the fallback mechanism
//         const FORCE_DUMMY_DATA_FOR_TESTING = false; // Disabled to see natural flow
//         const FORCE_SEARCH_LIST_TESTING = false; // Enable to force search-list API testing

//         if (FORCE_DUMMY_DATA_FOR_TESTING) {
//           throw new Error('Forcing fallback to dummy data for testing');
//         }

//         // Force search-list testing by creating a mock TUI response
//         if (FORCE_SEARCH_LIST_TESTING) {
//           searchResponse = {
//             success: true,
//             message: 'Mock search completed for search-list testing',
//             data: {
//               onwardFlights: [], // Will be populated by search-list
//               returnFlights: undefined,
//               searchId: 'test-search-id',
//               totalResults: 0,
//               tui: 'TEST_TUI_FOR_SEARCH_LIST', // Mock TUI for testing
//               filters: {
//                 airlines: [],
//                 priceRange: { min: 0, max: 100000 },
//                 durationRange: { min: 0, max: 1440 },
//                 stops: [],
//                 departureTimeSlots: [],
//                 arrivalTimeSlots: []
//               }
//             }
//           };
//         } else {
//           // Normal flow - try to call the real search API
//           searchResponse = await flightService.searchFlights(searchRequest);
//         }
//       } catch (searchError) {
//         console.error('❌ Search API failed, falling back to dummy data:', searchError);

//         // If search API fails, create a response using dummy data
//         const dummySearchListData = await loadDummySearchListData();
//         const dummyFlights = transformSearchListResponseToFlights(dummySearchListData);

//         searchResponse = {
//           success: true,
//           message: 'Using dummy data due to search API failure',
//           data: {
//             onwardFlights: dummyFlights,
//             returnFlights: undefined,
//             searchId: 'dummy-search-id',
//             totalResults: dummyFlights.length,
//             tui: dummySearchListData.TUI,
//             filters: {
//               airlines: [...new Set(dummyFlights.map(f => f.airline))],
//               priceRange: {
//                 min: Math.min(...dummyFlights.map(f => f.price)),
//                 max: Math.max(...dummyFlights.map(f => f.price))
//               },
//               durationRange: { min: 120, max: 300 },
//               stops: [...new Set(dummyFlights.map(f => f.stops))],
//               departureTimeSlots: ['morning', 'afternoon', 'evening'],
//               arrivalTimeSlots: ['morning', 'afternoon', 'evening']
//             }
//           }
//         };


//         // Return with the dummy search-list response as well
//         return { searchResponse, searchListResponse: dummySearchListData };
//       }

//       // If search was successful and we have a TUI, call search-list
//       if (searchResponse.success && searchResponse.data?.tui) {
//         try {
//           const searchListResponse = await flightService.searchList(searchResponse.data.tui, 30);
//           return { searchResponse, searchListResponse };
//         } catch (searchListError) {
//           console.error('❌ Search-list failed, returning search response only:', searchListError);
//           return { searchResponse };
//         }
//       }

//       return { searchResponse };
//     } catch (error) {
//       console.error('Error in searchFlightsWithSearchList, falling back to dummy data:', error);

//       // Final fallback - if everything fails, use dummy data
//       try {
//         const dummySearchListData = await loadDummySearchListData();
//         const dummyFlights = transformSearchListResponseToFlights(dummySearchListData);

//         const fallbackSearchResponse: FlightSearchResponse = {
//           success: true,
//           message: 'Using dummy data due to complete API failure',
//           data: {
//             onwardFlights: dummyFlights,
//             returnFlights: undefined,
//             searchId: 'fallback-search-id',
//             totalResults: dummyFlights.length,
//             tui: dummySearchListData.TUI,
//             filters: {
//               airlines: [...new Set(dummyFlights.map(f => f.airline))],
//               priceRange: {
//                 min: Math.min(...dummyFlights.map(f => f.price)),
//                 max: Math.max(...dummyFlights.map(f => f.price))
//               },
//               durationRange: { min: 120, max: 300 },
//               stops: [...new Set(dummyFlights.map(f => f.stops))],
//               departureTimeSlots: ['morning', 'afternoon', 'evening'],
//               arrivalTimeSlots: ['morning', 'afternoon', 'evening']
//             }
//           }
//         };

//         return { searchResponse: fallbackSearchResponse, searchListResponse: dummySearchListData };
//       } catch (fallbackError) {
//         console.error('Fallback also failed:', fallbackError);
//         throw error; // Re-throw original error if fallback fails
//       }
//     }
//   },

//   // Preview API integration with fallback to dummy data - API first approach
//   async getPreviewData(tui?: string, from?: string, to?: string): Promise<PreviewResponse> {
//     try {

//       // API-first approach: Try API first, fallback to JSON automatically
//       // Set to true only if you want to force JSON loading for testing
//       const FORCE_PREVIEW_DUMMY_DATA_FOR_TESTING = false; // Set to false for API-first approach

//       if (FORCE_PREVIEW_DUMMY_DATA_FOR_TESTING) {
//         throw new Error('Forcing JSON fallback for testing');
//       }

//       // Build query parameters for API call
//       const queryParams = new URLSearchParams();
//       if (tui) queryParams.append('tui', tui);
//       if (from) queryParams.append('from', from);
//       if (to) queryParams.append('to', to);

//       // Use the v1 API structure - if TUI is provided, use the dynamic route
//       const apiUrl = tui
//         ? `/api/v1/preview/${tui}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
//         : `/api/v1/preview${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;


//       // Call the actual preview API
//       const apiResponse = await fetch(apiUrl, {
//         method: 'GET',
//         headers: {
//           'Content-Type': 'application/json',
//           // Add any authentication headers here if needed
//         },
//       });

//       if (!apiResponse.ok) {
//         throw new Error(`Preview API failed: ${apiResponse.status} ${apiResponse.statusText}`);
//       }

//       const apiData: PreviewResponse = await apiResponse.json();

//       if (apiData.success && apiData.data) {
//         return apiData;
//       } else {
//         throw new Error(apiData.message || 'API returned unsuccessful response');
//       }

//     } catch (apiError) {
//       console.warn('⚠️ Preview API failed, falling back to dummy data:', apiError);

//       // Fallback: Load dummy data from JSON file
//       const dummyPreviewData = await loadDummyPreviewData();

//       // Apply any filtering based on parameters if needed
//       // Since the new structure is a single object, we return it as-is
//       // You can add filtering logic here if needed based on your requirements

//       const previewResponse: PreviewResponse = {
//         success: true,
//         message: 'Preview data loaded from previewdummy.json file (API not available)',
//         data: dummyPreviewData
//       };


//       return previewResponse;
//     }
//   }
// };

// // ============================================================================
// // HELPER FUNCTIONS FOR FLIGHT PROCESSING
// // ============================================================================

// /**
//  * Normalizes incoming flights from different providers to ensure consistent structure
//  * Handles missing fields, validates data types, and applies fallbacks
//  */
// export const normalizeFlights = (flights: Flight[]): Flight[] => {
//   if (!Array.isArray(flights)) {
//     console.warn('normalizeFlights: Input is not an array, returning empty array');
//     return [];
//   }

//   return flights.map((flight, index) => {
//     try {
//       // Ensure required fields exist with fallbacks
//       const normalizedFlight: Flight = {
//         id: flight.id || `flight-${Date.now()}-${index}`,
//         airline: flight.airline || 'Unknown',
//         logo: flight.logo || '/images/default-airline.png',
//         flightNumber: flight.flightNumber || '0000',
//         segments: Array.isArray(flight.segments) ? flight.segments : [],
//         departure: {
//           time: flight.departure?.time || '00:00',
//           airport: flight.departure?.airport || 'UNK',
//           city: flight.departure?.city || 'Unknown',
//           date: flight.departure?.date || new Date().toISOString().split('T')[0],
//           terminal: flight.departure?.terminal
//         },
//         arrival: {
//           time: flight.arrival?.time || '00:00',
//           airport: flight.arrival?.airport || 'UNK',
//           city: flight.arrival?.city || 'Unknown',
//           date: flight.arrival?.date || new Date().toISOString().split('T')[0],
//           terminal: flight.arrival?.terminal
//         },
//         duration: flight.duration || '0h 00m',
//         stops: flight.stops || '0',
//         price: typeof flight.price === 'number' && flight.price > 0 ? flight.price : 0,
//         originalPrice: typeof flight.originalPrice === 'number' ? flight.originalPrice : undefined,
//         currency: flight.currency || 'INR',
//         baggage: {
//           cabin: flight.baggage?.cabin || '7 kg',
//           checkin: flight.baggage?.checkin || '15 kg'
//         },
//         cancellation: flight.cancellation || 'Non-refundable',
//         rating: typeof flight.rating === 'number' ? flight.rating : 4.0,
//         features: Array.isArray(flight.features) ? flight.features : [],
//         fares: Array.isArray(flight.fares) ? flight.fares : [],
//         isRefundable: Boolean(flight.isRefundable),
//         fareType: flight.fareType || 'Economy'
//       };

//       return normalizedFlight;
//     } catch (error) {
//       console.error(`Error normalizing flight at index ${index}:`, error, flight);
//       // Return a minimal valid flight object as fallback
//       return {
//         id: `fallback-${Date.now()}-${index}`,
//         airline: 'Unknown',
//         logo: '/images/default-airline.png',
//         flightNumber: '0000',
//         segments: [],
//         departure: {
//           time: '00:00',
//           airport: 'UNK',
//           city: 'Unknown',
//           date: new Date().toISOString().split('T')[0]
//         },
//         arrival: {
//           time: '00:00',
//           airport: 'UNK',
//           city: 'Unknown',
//           date: new Date().toISOString().split('T')[0]
//         },
//         duration: '0h 00m',
//         stops: '0',
//         price: 0,
//         currency: 'INR',
//         baggage: { cabin: '7 kg', checkin: '15 kg' },
//         cancellation: 'Non-refundable',
//         rating: 4.0,
//         features: [],
//         fares: [],
//         isRefundable: false,
//         fareType: 'Economy'
//       };
//     }
//   });
// };

// /**
//  * Removes duplicate flights based on multiple criteria
//  * Priority: flightNumber + departure time + airline, then id
//  */
// export const dedupeFlights = (flights: Flight[]): Flight[] => {
//   if (!Array.isArray(flights) || flights.length === 0) {
//     return [];
//   }

//   const seen = new Set<string>();
//   const uniqueFlights: Flight[] = [];

//   for (const flight of flights) {
//     // Create composite key for deduplication
//     const compositeKey = `${flight.airline}-${flight.flightNumber}-${flight.departure.time}-${flight.departure.date}-${flight.arrival.time}`;
//     const idKey = flight.id;

//     // Check both composite key and ID
//     if (!seen.has(compositeKey) && !seen.has(idKey)) {
//       seen.add(compositeKey);
//       seen.add(idKey);
//       uniqueFlights.push(flight);
//     }
//   }

//   return uniqueFlights;
// };

// /**
//  * Sorts flights by price (lowest first), then by duration as secondary criteria
//  */
// export const sortFlightsByPrice = (flights: Flight[]): Flight[] => {
//   if (!Array.isArray(flights)) {
//     return [];
//   }

//   return [...flights].sort((a, b) => {
//     // Primary sort: price (ascending)
//     const priceDiff = a.price - b.price;
//     if (priceDiff !== 0) return priceDiff;

//     // Secondary sort: duration (ascending)
//     const getDurationMinutes = (duration: string): number => {
//       const match = duration.match(/(\d+)h\s*(\d+)m/);
//       if (match) {
//         return parseInt(match[1]) * 60 + parseInt(match[2]);
//       }
//       return 0;
//     };

//     return getDurationMinutes(a.duration) - getDurationMinutes(b.duration);
//   });
// };

// // ============================================================================
// // MULTI-PROVIDER FLIGHT PROCESSING WITH CORRECT BRANCHING LOGIC
// // ============================================================================

// /**
//  * Main function to process flight list from providers
//  * Handles the complete flow with proper branching logic exactly as specified
//  */
// export const processFlightList = (
//   flightList: Flight[],
//   isMaster: boolean,
//   currentMasterState?: { flights: Flight[]; totalCount: number },
//   activeFilters?: {
//     airlines: string[];
//     priceRange: [number, number];
//     stops: string[];
//     departure: string[];
//     arrival: string[];
//     refundableOnly: boolean;
//   },
//   currentPage: number = 1,
//   pageSize: number = 20
// ): {
//   masterState: { flights: Flight[]; totalCount: number };
//   filteredFlights?: Flight[];
//   paginatedResult?: { flights: Flight[]; currentPage: number; totalPages: number; hasMore: boolean };
//   shouldApplyFilters: boolean;
// } => {

//   // Handle edge cases
//   if (!Array.isArray(flightList)) {
//     console.warn('⚠️ flightList is not an array, treating as empty');
//     flightList = [];
//   }

//   try {
//     if (!isMaster) {
//       // ============================================================================
//       // CASE A: FIRST PROVIDER (isMaster = false)
//       // ============================================================================

//       // Step 1: Normalize
//       const normalizedFlights = normalizeFlights(flightList);
//       console.log(`✅ Step 1 - Normalized: ${normalizedFlights.length} flights`);

//       // Step 2: Sort before saving (as specified)
//       const sortedFlights = sortFlightsByPrice(normalizedFlights);

//       // Step 3: Save to master state
//       const newMasterState = {
//         flights: sortedFlights,
//         totalCount: sortedFlights.length
//       };

//       // Step 4: Build filters (would be implemented based on flight data)

//       // Step 5: Apply filters if provided
//       let filteredFlights = sortedFlights;
//       if (activeFilters) {
//         filteredFlights = applyFiltersToFlights(sortedFlights, activeFilters);
//       }

//       // Step 6: Paginate (20 per batch)
//       const paginatedResult = paginateFlights(filteredFlights, currentPage, pageSize);

//       // Step 7: Ready to display

//       return {
//         masterState: newMasterState,
//         filteredFlights,
//         paginatedResult,
//         shouldApplyFilters: false // Already applied
//       };

//     } else {
//       // ============================================================================
//       // CASE B: SUBSEQUENT PROVIDERS (isMaster = true)
//       // ============================================================================

//       if (!currentMasterState) {
//         throw new Error('currentMasterState is required when isMaster is true');
//       }

//       // Step 1: Normalize incoming flights
//       const normalizedFlights = normalizeFlights(flightList);
//       console.log(`✅ Step 1 - Normalized: ${normalizedFlights.length} flights`);

//       // Step 2: Merge with existing master list
//       const combinedFlights = [...currentMasterState.flights, ...normalizedFlights];

//       // Step 3: Deduplicate
//       const dedupedFlights = dedupeFlights(combinedFlights);

//       // Step 4: Sort after merge (as specified)
//       const sortedFlights = sortFlightsByPrice(dedupedFlights);

//       // Step 5: Update filters (build new filter options from updated master list)

//       // Step 6: Stop here - don't re-run filters/pagination
//       const newMasterState = {
//         flights: sortedFlights,
//         totalCount: sortedFlights.length
//       };

//       return {
//         masterState: newMasterState,
//         shouldApplyFilters: true // Signal that UI should reapply filters
//       };
//     }
//   } catch (error) {
//     console.error('❌ Error in processFlightList:', error);

//     // Return safe fallback state
//     const fallbackState = currentMasterState || {
//       flights: [],
//       totalCount: 0
//     };

//     return {
//       masterState: fallbackState,
//       shouldApplyFilters: false
//     };
//   }
// };

// /**
//  * Helper function to apply filters to flights
//  */
// const applyFiltersToFlights = (flights: Flight[], filters: {
//   airlines: string[];
//   priceRange: [number, number];
//   stops: string[];
//   departure: string[];
//   arrival: string[];
//   refundableOnly: boolean;
// }): Flight[] => {
//   let filtered = [...flights];

//   // Filter by airlines
//   if (filters.airlines.length > 0) {
//     filtered = filtered.filter(flight => filters.airlines.includes(flight.airline));
//   }

//   // Filter by price range
//   if (filters.priceRange && filters.priceRange.length === 2) {
//     const [minPrice, maxPrice] = filters.priceRange;
//     filtered = filtered.filter(flight => flight.price >= minPrice && flight.price <= maxPrice);
//   }

//   // Filter by stops
//   if (filters.stops.length > 0) {
//     filtered = filtered.filter(flight => filters.stops.includes(flight.stops));
//   }

//   // Filter by refundable only
//   if (filters.refundableOnly) {
//     filtered = filtered.filter(flight => flight.isRefundable);
//   }

//   return filtered;
// };

// /**
//  * Helper function to paginate flights
//  */
// const paginateFlights = (flights: Flight[], page: number, pageSize: number) => {
//   const totalFlights = flights.length;
//   const totalPages = Math.ceil(totalFlights / pageSize);
//   const currentPage = Math.max(1, Math.min(page, totalPages));

//   const startIndex = (currentPage - 1) * pageSize;
//   const endIndex = startIndex + pageSize;
//   const paginatedFlights = flights.slice(startIndex, endIndex);

//   return {
//     flights: paginatedFlights,
//     currentPage,
//     totalPages,
//     hasMore: currentPage < totalPages
//   };
// };

// /**
//  * Enhanced search with multiple provider support
//  */
// export const searchFlightsFromMultipleProviders = async (
//   searchRequest: FlightSearchRequest,
//   providers: string[] = ['primary']
// ): Promise<{ flights: Flight[]; totalCount: number }> => {
//   try {

//     // For now, use the main search service
//     // In the future, this can be extended to call multiple provider APIs
//     const { searchResponse, searchListResponse } = await flightService.searchFlightsWithSearchList(searchRequest);

//     let flights: Flight[] = [];

//     // Extract flights from response
//     if (searchListResponse && searchListResponse.Trips && searchListResponse.Trips.length > 0) {
//       flights = transformSearchListResponseToFlights(searchListResponse);
//     } else if (searchResponse.data?.onwardFlights) {
//       flights = searchResponse.data.onwardFlights;
//     }

//     // Process as single provider response using Case A (isMaster = false)
//     const result = processFlightList(flights, false);

//     return {
//       flights: result.masterState.flights,
//       totalCount: result.masterState.totalCount
//     };

//   } catch (error) {
//     console.error('Error in multi-provider search:', error);

//     return {
//       flights: [],
//       totalCount: 0
//     };
//   }
// };

// // Helper function to load domestic round trip dummy data
// export const loadDomesticRoundTripData = async () => {
//   try {

//     const response = await fetch('/assets/json/dummydata/domestic-roundtrip.json');
//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const dummyData = await response.json();

//     return dummyData;
//   } catch (error) {
//     console.error('❌ Error loading domestic round trip data:', error);

//     // Return minimal fallback response if file loading fails
//     return {
//       success: false,
//       message: 'Fallback data - domestic round trip JSON file not available',
//       data: {
//         searchInfo: {
//           tripType: 'roundTrip',
//           flightType: 'domestic',
//           searchDate: new Date().toISOString().split('T')[0],
//           currency: 'INR'
//         },
//         onwardFlights: [],
//         returnFlights: []
//       }
//     };
//   }
// };

// // Helper function to load international round trip dummy data
// export const loadInternationalRoundTripData = async () => {
//   try {

//     const response = await fetch('/assets/json/dummydata/international-roundtrip.json');
//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const dummyData = await response.json();

//     return dummyData;
//   } catch (error) {
//     console.error('❌ Error loading international round trip data:', error);

//     // Return minimal fallback response if file loading fails
//     return {
//       success: false,
//       message: 'Fallback data - international round trip JSON file not available',
//       data: {
//         searchInfo: {
//           tripType: 'roundTrip',
//           flightType: 'international',
//           searchDate: new Date().toISOString().split('T')[0],
//           currency: 'INR'
//         },
//         onwardFlights: [],
//         returnFlights: []
//       }
//     };
//   }
// };

// // Helper function to determine if a route is international
// export const isInternationalRoute = (from: string, to: string): boolean => {
//   // List of major Indian airport codes
//   const indianAirports = [
//     'DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'AMD', 'COK', 'GOI', 'PNQ',
//     'JAI', 'LKO', 'IXC', 'GAU', 'IXB', 'IXA', 'IXJ', 'IXL', 'IXM', 'IXR',
//     'IXS', 'IXU', 'IXW', 'IXY', 'IXZ', 'JDH', 'JGA', 'JLR', 'JSA', 'JRH',
//     'KNU', 'IXD', 'IXE', 'IXG', 'IXH', 'IXI', 'IXK', 'IXN', 'IXP', 'IXQ',
//     'IXT', 'IXV', 'CJB', 'TRV', 'VNS', 'RPR', 'UDR', 'VGA', 'VTZ'
//   ];

//   const isFromIndian = indianAirports.includes(from.toUpperCase());
//   const isToIndian = indianAirports.includes(to.toUpperCase());

//   // International if one is Indian and other is not, or both are non-Indian
//   return !(isFromIndian && isToIndian);
// };

// // Main function to load round trip data based on route type
// export const loadRoundTripData = async (from: string, to: string) => {
//   const isInternational = isInternationalRoute(from, to);


//   if (isInternational) {
//     return await loadInternationalRoundTripData();
//   } else {
//     return await loadDomesticRoundTripData();
//   }
// };

// export default flightService;




