
'use client';

const services = [
  {
    icon: 'ri-global-line',
    title: 'Where2Go',
    description: 'Find routes to anywhere'
  },
  {
    icon: 'ri-route-line',
    title: 'How2Go',
    badge: 'new',
    description: 'Find routes to anywhere'
  },
  {
    icon: 'ri-group-line',
    title: 'MICE',
    description: 'Offsites, Events & Meetings'
  },
  {
    icon: 'ri-gift-line',
    title: 'Gift Cards'
  }
];

export default function QuickServices() {
  return (
    <div className="bg-gray-800 py-4 md:py-6">
      {/* Desktop Layout */}
      <div className="hidden md:flex items-center justify-center space-x-12">
        {services.map((service, index) => (
          <div key={index} className="flex items-center space-x-3 text-white cursor-pointer hover:text-[#013688]/70 transition-colors">
            <div className="w-10 h-10 flex items-center justify-center bg-white/10 rounded-full relative">
              <i className={`${service.icon} text-xl`}></i>
              {service.badge && (
                <span className="absolute -top-1 -right-1 bg-pink-500 text-white text-xs px-1 rounded">
                  {service.badge}
                </span>
              )}
            </div>
            <div>
              <div className="font-medium text-sm">{service.title}</div>
              {service.description && (
                <div className="text-xs opacity-80">{service.description}</div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Mobile Layout - Scrollable */}
      <div className="md:hidden overflow-x-auto px-4">
        <div className="flex space-x-6 min-w-max">
          {services.map((service, index) => (
            <div key={index} className="flex flex-col items-center text-white cursor-pointer hover:text-[#013688]/70 transition-colors min-w-[80px]">
              <div className="w-10 h-10 flex items-center justify-center bg-white/10 rounded-full relative mb-2">
                <i className={`${service.icon} text-lg`}></i>
                {service.badge && (
                  <span className="absolute -top-1 -right-1 bg-pink-500 text-white text-xs px-1 rounded">
                    {service.badge}
                  </span>
                )}
              </div>
              <div className="text-center">
                <div className="font-medium text-xs leading-tight">{service.title}</div>
                {service.description && (
                  <div className="text-xs opacity-80 mt-1 hidden">{service.description}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
