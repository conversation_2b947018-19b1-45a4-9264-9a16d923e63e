
'use client';

import { useState, useRef, useEffect } from 'react';

const offerCategories = [
  { id: 'hot-deal', label: 'HOT DEAL', active: true },
  { id: 'flight', label: 'FLIGHT' },
  { id: 'hotel', label: 'HOTEL' },
  { id: 'holidays', label: 'HOLIDAYS' },
  { id: 'visa', label: 'VISA' }
];

const exclusiveDeals = [
  {
    id: 1,
    category: 'FLIGHT',
    airline: 'AIR INDIA',
    title: 'Special Air India Fares',
    description: 'Fly Mumbai - Jeddah - Mumbai With Exclusive Rates',
    highlight: "Don't Miss!",
    highlightSub: 'Special Air India Fares',
    action: 'Book Now',
    price: 'From ₹15,999',
    bgImage: 'https://readdy.ai/api/search-image?query=Air%20India%20aircraft%20on%20runway%20with%20red%20and%20white%20livery%2C%20professional%20aviation%20photography%2C%20modern%20commercial%20airplane%2C%20Indian%20airline%20branding%2C%20clean%20airport%20environment%2C%20travel%20industry%20aesthetic&width=200&height=140&seq=airindia1&orientation=landscape'
  },
  {
    id: 2,
    category: 'FLIGHT',
    airline: 'SCOOT',
    title: 'Special Fares by Scoot Airlines',
    description: 'Fares starting at INR 5,900*',
    highlight: 'Special Fares',
    highlightSub: 'Fares starting at INR 5,900*',
    action: 'Book Now',
    price: 'From ₹5,900',
    bgImage: 'https://readdy.ai/api/search-image?query=Scoot%20Airlines%20yellow%20aircraft%20taking%20off%2C%20budget%20airline%20branding%2C%20modern%20low-cost%20carrier%2C%20Singapore%20aviation%2C%20affordable%20travel%20options%2C%20commercial%20flight%20departure&width=200&height=140&seq=scoot1&orientation=landscape'
  },
  {
    id: 3,
    category: 'FLIGHT',
    airline: 'AIR CANADA',
    title: 'Connecting India to Canada',
    description: 'Direct Mumbai to Toronto Via London',
    highlight: 'Connecting',
    highlightSub: 'India to Canada',
    action: 'Book Now',
    price: 'From ₹85,999',
    bgImage: 'https://readdy.ai/api/search-image?query=Air%20Canada%20aircraft%20with%20maple%20leaf%20logo%2C%20international%20long-haul%20flight%2C%20Canada%20airline%20branding%2C%20modern%20wide-body%20aircraft%2C%20professional%20aviation%20photography%2C%20premium%20travel%20service&width=200&height=140&seq=aircanada1&orientation=landscape'
  },
  {
    id: 4,
    category: 'HOTEL',
    airline: 'HOTELS',
    title: 'Luxury Hotel Deals',
    description: 'Up to 50% OFF on Premium Hotels',
    highlight: 'Luxury Stays',
    highlightSub: 'Premium Locations',
    action: 'Book Now',
    price: 'Up to 50% OFF',
    bgImage: 'https://readdy.ai/api/search-image?query=Luxury%20hotel%20exterior%20with%20elegant%20architecture%2C%20upscale%20hospitality%2C%20premium%20accommodation%2C%20modern%20hotel%20design%2C%20five-star%20service%2C%20sophisticated%20travel%20experience&width=200&height=140&seq=hotel1&orientation=landscape'
  },
  {
    id: 5,
    category: 'HOLIDAYS',
    airline: 'PACKAGES',
    title: 'Holiday Packages',
    description: 'Complete vacation packages with flights and hotels',
    highlight: 'All Inclusive',
    highlightSub: 'Holiday Deals',
    action: 'Book Now',
    price: 'From ₹25,999',
    bgImage: 'https://readdy.ai/api/search-image?query=Tropical%20vacation%20destination%20with%20palm%20trees%2C%20beach%20resort%2C%20holiday%20paradise%2C%20leisure%20travel%2C%20vacation%20packages%2C%20tourism%20promotion%2C%20relaxing%20getaway%20atmosphere&width=200&height=140&seq=holiday1&orientation=landscape'
  },
  {
    id: 6,
    category: 'VISA',
    airline: 'SERVICES',
    title: 'Visa Services',
    description: 'Quick and reliable visa processing',
    highlight: 'Fast Track',
    highlightSub: 'Visa Processing',
    action: 'Apply Now',
    price: 'From ₹2,999',
    bgImage: 'https://readdy.ai/api/search-image?query=International%20passport%20with%20visa%20stamps%2C%20travel%20documents%2C%20visa%20application%20process%2C%20official%20travel%20paperwork%2C%20immigration%20services%2C%20professional%20documentation&width=200&height=140&seq=visa1&orientation=landscape'
  }
];

export default function OffersSection() {
  const [activeCategory, setActiveCategory] = useState('hot-deal');
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -340, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 340, behavior: 'smooth' });
    }
  };

  // Add mouse wheel scroll functionality
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      scrollContainer.scrollBy({ left: e.deltaY * 2, behavior: 'smooth' });
    };

    scrollContainer.addEventListener('wheel', handleWheel);
    return () => scrollContainer.removeEventListener('wheel', handleWheel);
  }, []);

  const filteredDeals = activeCategory === 'hot-deal' 
    ? exclusiveDeals 
    : exclusiveDeals.filter(deal => deal.category.toLowerCase() === activeCategory);

  return (
    <div className="py-8 md:py-12 px-4 md:px-6 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 md:mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
            Exclusive <span className="text-primary">Deals</span>
          </h2>
          <button className="flex items-center space-x-2 text-primary font-medium hover:text-primary/80">
            <span className="text-sm md:text-base">View All Deals</span>
            <div className="w-4 md:w-5 h-4 md:h-5 flex items-center justify-center">
              <i className="ri-arrow-right-line"></i>
            </div>
          </button>
        </div>

        {/* Category Tabs */}
        <div className="mb-6 md:mb-8">
          <div className="flex items-center space-x-6 md:space-x-8 overflow-x-auto">
            {offerCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`pb-3 px-1 font-bold transition-colors relative whitespace-nowrap text-sm md:text-base ${
                  activeCategory === category.id
                    ? 'text-primary border-b-3 border-primary'
                    : 'text-gray-600 hover:text-primary'
                } ${category.id === 'hot-deal' ? 'text-primary' : ''}`}
                style={{ borderBottomWidth: activeCategory === category.id ? '3px' : '0' }}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons */}
          <button
            onClick={scrollLeft}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all border hover:scale-105"
            style={{ marginLeft: '-20px' }}
          >
            <div className="w-5 h-5 flex items-center justify-center">
              <i className="ri-arrow-left-line text-gray-600"></i>
            </div>
          </button>
          <button
            onClick={scrollRight}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all border hover:scale-105"
            style={{ marginRight: '-20px' }}
          >
            <div className="w-5 h-5 flex items-center justify-center">
              <i className="ri-arrow-right-line text-gray-600"></i>
            </div>
          </button>

          {/* Deals Carousel */}
          <div
            ref={scrollContainerRef}
            className="flex space-x-4 overflow-x-auto pb-4 cursor-grab active:cursor-grabbing scrollbar-hide"
            style={{ 
              scrollSnapType: 'x mandatory'
            }}
          >
            {filteredDeals.map((deal) => (
              <div
                key={deal.id}
                className="flex-shrink-0 w-80 h-36 bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all hover:scale-105"
                style={{ scrollSnapAlign: 'start' }}
              >
                <div className="flex h-full">
                  {/* Left Image Section */}
                  <div className="w-2/5 relative">
                    <div
                      className="h-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${deal.bgImage})` }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-black/40 to-transparent"></div>
                      
                      {/* Category Tag */}
                      <div className="absolute top-2 left-2">
                        <div className="bg-[#013688] text-white px-2 py-1 rounded text-xs font-bold">
                          {deal.category}
                        </div>
                      </div>

                      {/* Airline/Service Badge */}
                      {deal.id <= 3 && (
                        <div className="absolute bottom-2 left-2">
                          <div className="bg-white/20 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium">
                            {deal.airline}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Right Content Section */}
                  <div className="w-3/5 p-4 flex flex-col justify-between">
                    <div>
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-sm font-bold text-gray-900 leading-tight">{deal.title}</h3>
                        <div className="bg-secondary text-white px-2 py-1 rounded text-xs font-bold ml-2">
                          {deal.highlight}
                        </div>
                      </div>
                      <p className="text-gray-600 text-xs mb-2 line-clamp-2">{deal.description}</p>
                      <div className="text-[#e74c3c] font-bold text-sm mb-3">{deal.price}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Custom Scrollbar Styles */}
        <style jsx>{`
          .scrollbar-hide {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
          }
          .scrollbar-hide::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
          }
        `}</style>
      </div>
    </div>
  );
}
