export interface FlightApiResponseRoundTripDomestic {
  searchResult: SearchResult;
  status: ApiStatus;
}

export interface ApiStatus {
  success: boolean;
  httpStatus: number;
}

export interface SearchResult {
  tripInfos: TripInfos;
}

export interface TripInfos {
  RETURN: FlightOption[];
}

export interface FlightOption {
  sI: SegmentInfo[];
  totalPriceList: FareOption[];
  airFlowType: string;
}

export interface SegmentInfo {
  id: string;
  fD: FlightDetails;
  stops: number;
  so: any[];
  duration: number;
  da: AirportInfo;
  aa: AirportInfo;
  dt: string;
  at: string;
  iand: boolean;
  isRs: boolean;
  sN: number;
}

export interface FlightDetails {
  aI: AirlineInfo;
  fN: string;
  eT: string;
}

export interface AirlineInfo {
  code: string;
  name: string;
  isLcc: boolean;
}

export interface AirportInfo {
  code: string;
  name: string;
  cityCode: string;
  city: string;
  country: string;
  countryCode: string;
  terminal: string;
}

export interface FareOption {
  fd: FareDetailsByPaxType;
  fareIdentifier: string;
  id: string;
  msri: any[];
  messages: any[];
  tai: TravelAncillaryInfo;
  icca: boolean;
}

export interface FareDetailsByPaxType {
  ADULT: PassengerFareDetails;
  CHILD: PassengerFareDetails;
}

export interface PassengerFareDetails {
  fC: FareComponents;
  afC: AdditionalFareCharges;
  bI: BaggageInfo;
  isHB?: boolean;
  rT: number;
  cc: string;
  cB: string;
  fB: string;
  mI: boolean;
  tjFlexFareBenefit?: string;
}

export interface FareComponents {
  BF: number;
  NF: number;
  TAF: number;
  TF: number;
  FCMU?: number;
  FCGST?: number;
  FACF?: number;
  FC?: number;
  FCMUGST?: number;
}

export interface AdditionalFareCharges {
  TAF: TaxBreakdown;
}

export interface TaxBreakdown {
  MFT: number;
  OT: number;
  MF: number;
  FTC?: number;
}

export interface BaggageInfo {
  iB: string;
  cB: string;
}

export interface TravelAncillaryInfo {
  tbi: {
    [segmentId: string]: SegmentBaggagePerPax[];
  };
}

export interface SegmentBaggagePerPax {
  ADULT?: BaggageInfo;
  CHILD?: BaggageInfo;
}






// international return trip
export interface FlightApiResponseInternational {
  searchResult: SearchResultInternational;
  status: ApiStatusInternational;
}

export interface ApiStatusInternational {
  success: boolean;
  httpStatus: number;
}

export interface SearchResultInternational {
  tripInfos: TripInfosInternational;
}

export interface TripInfosInternational {
  COMBO: FlightOptionInternational[];
}

export interface FlightOptionInternational {
  sI: SegmentInfoInternational[];
  totalPriceList: FareOptionInternational[];
  airFlowType: string;
}

export interface SegmentInfoInternational {
  id: string;
  fD: FlightDetailsInternational;
  stops: number;
  so: any[];
  duration: number;
  da: AirportInfoInternational;
  aa: AirportInfoInternational;
  dt: string;
  at: string;
  iand: boolean;
  isRs: boolean;
  sN: number;
  oda?: string; // Optional field found in this data
  oaa?: string; // Optional field found in this data
}

export interface FlightDetailsInternational {
  aI: AirlineInfoInternational;
  fN: string;
  eT: string;
}

export interface AirlineInfoInternational {
  code: string;
  name: string;
  isLcc: boolean;
}

export interface AirportInfoInternational {
  code: string;
  name: string;
  cityCode: string;
  city: string;
  country: string;
  countryCode: string;
  terminal: string;
}

export interface FareOptionInternational {
  fd: FareDetailsByPaxTypeInternational;
  fareIdentifier: string;
  id: string;
  msri: any[];
  messages: any[];
  tai: TravelAncillaryInfoInternational;
  icca: boolean;
}

export interface FareDetailsByPaxTypeInternational {
  ADULT: PassengerFareDetailsInternational;
  CHILD?: PassengerFareDetailsInternational; // Optional
  INFANT?: PassengerFareDetailsInternational; // Optional
}

export interface PassengerFareDetailsInternational {
  fC: FareComponentsInternational;
  afC: AdditionalFareChargesInternational;
  bI: BaggageInfoInternational;
  isHB?: boolean;
  rT: number;
  cc: string;
  cB: string;
  fB: string;
  mI: boolean;
  tjFlexFareBenefit?: string;
}

export interface FareComponentsInternational {
  BF: number;
  NF: number;
  TAF: number;
  TF: number;
  FCMU?: number;
  FCGST?: number;
  FACF?: number;
  FC?: number;
  FCMUGST?: number;
}

export interface AdditionalFareChargesInternational {
  TAF: TaxBreakdownInternational;
}

export interface TaxBreakdownInternational {
  MFT: number;
  OT: number;
  MF: number;
  FTC?: number;
}

export interface BaggageInfoInternational {
  iB?: string; // Made optional (not present in all fares)
  cB: string;
}

export interface TravelAncillaryInfoInternational {
  tbi: {
    [segmentId: string]: SegmentBaggagePerPaxInternational[];
  };
}

export interface SegmentBaggagePerPaxInternational {
  ADULT?: BaggageInfoInternational;
  CHILD?: BaggageInfoInternational;
}



// one way trip

export interface FlightApiResponseOneWay {
  tui: string;
  provider: string;
  status: string;
  searchResult: SearchResultWrapper;
}

export interface SearchResultWrapper {
  searchResult: SearchResultOneWay;
  status: ApiStatusOneWay;
  isComplete: boolean;
}

export interface ApiStatusOneWay {
  success: boolean;
  httpStatus: number;
}

export interface SearchResultOneWay {
  tripInfos: TripInfosOneWay;
}

export interface TripInfosOneWay {
  ONWARD: FlightOptionOneWay[];
  // RETURN would be here in a round-trip response using this schema
}

export interface FlightOptionOneWay {
  SegmentInformation: SegmentInformation[];
  totalPriceList: FareOptionOneWay[];
  airFlowType: string;
}

export interface SegmentInformation {
  id: string;
  FlightDesignator: FlightDesignator;
  OAC?: AirlineInfoOneWay; // Operating Airline Code (optional, as it's not in all segments)
  stops: number;
  StopOverairports: any[];
  duration: number;
  DepartureAirport: AirportInfoOneWay;
  ArrivalAirport: AirportInfoOneWay;
  DepartureTime: string;
  Arrivaltime: string;
  IsArrivingNextDay: boolean;
  isReturnSegment: boolean;
  SegmentNumber: number;
}

export interface FlightDesignator {
  MAC: AirlineInfoOneWay; // Marketing Airline Code
  FlightNumber: string;
  EquipmentType: string;
}

export interface AirlineInfoOneWay {
  code: string;
  isLcc: boolean;
  name: string;
}

export interface AirportInfoOneWay {
  city: string;
  cityCode?: string; // Optional in this response (missing from Arrival)
  code: string;
  country: string;
  countryCode: string;
  name: string;
  terminal?: string; // Optional
  timezoneId?: string; // Optional
}

export interface FareOptionOneWay {
  FareDetail: FareDetailsByPaxTypeOneWay;
  fareIdentifier: string;
  PriceId: string;
  MatchedSpecialReturnIdentifier: any[];
  messages: any[];
  TripAdditionalInformation: TripAdditionalInformation;
  icca: boolean;
}

export interface FareDetailsByPaxTypeOneWay {
  CHILD: PassengerFareDetailsOneWay;
  INFANT: PassengerFareDetailsOneWay;
  ADULT: PassengerFareDetailsOneWay;
}

export interface PassengerFareDetailsOneWay {
  fareComponents: FareComponentsOneWay;
  AdditionalFareComponents: AdditionalFareComponents;
  Baggageinformation: BaggageInformation;
  RefundableType: number;
  CabinClass: string;
  ClassOfBooking: string;
  FareBasis: string;
  "Seats Remaining"?: number; // Key has a space
  MealIndicator?: boolean; // Key has a space in some objects
  "MealIndicator "?: boolean; // Key has a space in others
  HandBaggageindicator?: boolean;
}

export interface FareComponentsOneWay {
  NetFare: number;
  TaxesAndFees: number;
  "TaxesAndFees "?: number; // Key has a space for INFANT
  BaseFare: number;
  TotalFare: number;
}

export interface AdditionalFareComponents {
  TaxesAndFees: TaxBreakdownOneWay;
}

export interface TaxBreakdownOneWay {
  FuelSurcharge?: number;
  ManagementFeeTax: number;
  OtherCharges?: number;
  ManagementFee: number;
  AirlineGSTComponent?: number;
  CarrierMiscFee?: number;
  "FlexTotalCharges "?: number;
}

export interface BaggageInformation {
  CheckingBaggage?: string; // Optional (not on INFANT fare)
  CabinBaggage: string;
}

export interface TripAdditionalInformation {
  TripBaggageInformation: {
    [segmentId: string]: SegmentBaggagePerPaxOneWay[];
  };
}

// This models the array like [{"INFANT": {...}}, {"ADULT": {...}}]
export interface SegmentBaggagePerPaxOneWay {
  INFANT?: SegmentBaggageDetails;
  ADULT?: SegmentBaggageDetails;
  CHILD?: SegmentBaggageDetails;
}

export interface SegmentBaggageDetails {
  cB: string;
  iB?: string; // Optional (not in all pax types)
}