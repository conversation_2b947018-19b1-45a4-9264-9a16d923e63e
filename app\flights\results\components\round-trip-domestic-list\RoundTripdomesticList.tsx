'use client';

import React, { useState, useMemo } from 'react';
import { FlightOption } from '@/app/models/flight-list.model';
import RoundtripDomesticCard from './components/RoundtripDomesticCard';
import { Filter, SortAsc, SortDesc } from 'lucide-react';

interface RoundTripDomesticListProps {
  onwardFlights: FlightOption[];
  returnFlights: FlightOption[];
  loading: boolean;
  error: string | null;
}

type SortOption = 'price' | 'duration' | 'departure' | 'arrival';
type SortOrder = 'asc' | 'desc';

export default function RoundTripDomesticList({
  onwardFlights,
  returnFlights,
  loading,
  error
}: RoundTripDomesticListProps) {
  const [activeTab, setActiveTab] = useState<'onward' | 'return'>('onward');
  const [showFilters, setShowFilters] = useState(false);

  // Filter states
  const [priceRange, setPriceRange] = useState([2000, 25000]);
  const [selectedAirlines, setSelectedAirlines] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<SortOption>('price');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');

  // Get unique airlines for filter
  const airlines = useMemo(() => {
    const currentFlights = activeTab === 'onward' ? onwardFlights : returnFlights;
    const airlineSet = new Set<string>();

    currentFlights.forEach(flight => {
      const segments = flight.sI || [];
      segments.forEach((segment: any) => {
        if (segment.fD?.aI?.name) {
          airlineSet.add(segment.fD.aI.name);
        }
      });
    });

    return Array.from(airlineSet);
  }, [onwardFlights, returnFlights, activeTab]);

  // Filter and sort flights
  const filteredAndSortedFlights = useMemo(() => {
    const currentFlights = activeTab === 'onward' ? onwardFlights : returnFlights;

    let filtered = currentFlights.filter(flight => {
      // Price filter
      const fareInfo = flight.totalPriceList?.[0];
      const fare = fareInfo?.fd?.ADULT?.fC?.TF || 0;
      if (fare < priceRange[0] || fare > priceRange[1]) return false;

      // Airline filter
      if (selectedAirlines.length > 0) {
        const segments = flight.sI || [];
        const hasSelectedAirline = segments.some((segment: any) =>
          selectedAirlines.includes(segment.fD?.aI?.name)
        );
        if (!hasSelectedAirline) return false;
      }

      return true;
    });

    // Sort flights
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'price':
          aValue = a.totalPriceList?.[0]?.fd?.ADULT?.fC?.TF || 0;
          bValue = b.totalPriceList?.[0]?.fd?.ADULT?.fC?.TF || 0;
          break;
        case 'duration':
          aValue = (a.sI || []).reduce((total: number, seg: any) => total + (seg.duration || 0), 0);
          bValue = (b.sI || []).reduce((total: number, seg: any) => total + (seg.duration || 0), 0);
          break;
        case 'departure':
          aValue = new Date(a.sI?.[0]?.dt || 0).getTime();
          bValue = new Date(b.sI?.[0]?.dt || 0).getTime();
          break;
        case 'arrival':
          const aSegments = a.sI || [];
          const bSegments = b.sI || [];
          aValue = new Date(aSegments[aSegments.length - 1]?.at || 0).getTime();
          bValue = new Date(bSegments[bSegments.length - 1]?.at || 0).getTime();
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

    return filtered;
  }, [onwardFlights, returnFlights, activeTab, priceRange, selectedAirlines, sortBy, sortOrder]);

  const handleAirlineToggle = (airline: string) => {
    setSelectedAirlines(prev =>
      prev.includes(airline)
        ? prev.filter(a => a !== airline)
        : [...prev, airline]
    );
  };

  const handleFlightSelect = (flight: FlightOption) => {
    console.log('Selected flight:', flight);
    // Handle flight selection logic here
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading flights</div>
        <div className="text-gray-600">{error}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('onward')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'onward'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Onward Flights ({onwardFlights.length})
        </button>
        <button
          onClick={() => setActiveTab('return')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'return'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Return Flights ({returnFlights.length})
        </button>
      </div>

      {/* Filters and Sort */}
      <div className="flex flex-wrap items-center justify-between gap-4 bg-white p-4 rounded-lg shadow-sm">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          <Filter className="w-4 h-4" />
          <span>Filters</span>
        </button>

        <div className="flex items-center space-x-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="price">Sort by Price</option>
            <option value="duration">Sort by Duration</option>
            <option value="departure">Sort by Departure</option>
            <option value="arrival">Sort by Arrival</option>
          </select>

          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Expandable Filters */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg shadow-sm space-y-4">
          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium mb-2">Price Range</label>
            <div className="px-3">
              <input
                type="range"
                min="2000"
                max="25000"
                step="500"
                value={priceRange[1]}
                onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-sm text-gray-600 mt-1">
                <span>₹{priceRange[0].toLocaleString()}</span>
                <span>₹{priceRange[1].toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Airlines */}
          {airlines.length > 0 && (
            <div>
              <label className="block text-sm font-medium mb-2">Airlines</label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {airlines.map(airline => (
                  <label key={airline} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedAirlines.includes(airline)}
                      onChange={() => handleAirlineToggle(airline)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{airline}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Clear Filters */}
          <button
            onClick={() => {
              setPriceRange([2000, 25000]);
              setSelectedAirlines([]);
            }}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Clear All Filters
          </button>
        </div>
      )}

      {/* Results */}
      <div className="space-y-4">
        {filteredAndSortedFlights.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-2">No flights found</div>
            <div className="text-sm text-gray-400">
              Try adjusting your filters or search criteria
            </div>
          </div>
        ) : (
          <>
            <div className="text-sm text-gray-600 mb-4">
              Showing {filteredAndSortedFlights.length} {activeTab} flights
            </div>
            {filteredAndSortedFlights.map((flight, index) => (
              <RoundtripDomesticCard
                key={`${activeTab}-${index}`}
                flight={flight}
                journeyType={activeTab}
                onSelect={handleFlightSelect}
              />
            ))}
          </>
        )}
      </div>
    </div>
  );
}