'use client';

import Image from "next/image";

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <Image
                src="/Unimoni-WIZZ-LOGO-reverse.webp"
                alt="Unimoni Logo"
                width={100}
                height={100}
                className="h-12 md:h-16 w-auto"
              />
              <p className="text-gray-300 mt-4 leading-relaxed">
                Your trusted travel companion for booking flights, hotels, and holiday packages. Experience seamless travel planning with our comprehensive services.
              </p>
            </div>
            <div className="flex space-x-4">
              <div className="w-10 h-10 flex items-center justify-center bg-gray-800 hover:bg-[#013688] rounded-full cursor-pointer transition-colors">
                <i className="ri-facebook-fill text-lg"></i>
              </div>
              <div className="w-10 h-10 flex items-center justify-center bg-gray-800 hover:bg-[#013688] rounded-full cursor-pointer transition-colors">
                <i className="ri-twitter-x-fill text-lg"></i>
              </div>
              <div className="w-10 h-10 flex items-center justify-center bg-gray-800 hover:bg-[#013688] rounded-full cursor-pointer transition-colors">
                <i className="ri-instagram-fill text-lg"></i>
              </div>
              <div className="w-10 h-10 flex items-center justify-center bg-gray-800 hover:bg-[#013688] rounded-full cursor-pointer transition-colors">
                <i className="ri-linkedin-fill text-lg"></i>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Our Services</h4>
            <ul className="space-y-3">
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Flight Booking</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Hotel Reservations</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Holiday Packages</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Train Booking</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Bus Tickets</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Car Rentals</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Travel Insurance</a></li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">My Bookings</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Flight Status</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Web Check-in</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Cancellation</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Customer Support</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Travel Guide</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#013688] transition-colors cursor-pointer">Special Offers</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-white">Contact Us</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-5 h-5 flex items-center justify-center mt-1">
                  <i className="ri-map-pin-fill text-[#013688]"></i>
                </div>
                <p className="text-gray-300">123 Travel Street, Business District, New York, NY 10001</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <i className="ri-phone-fill text-[#013688]"></i>
                </div>
                <p className="text-gray-300">+****************</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <i className="ri-mail-fill text-[#013688]"></i>
                </div>
                <p className="text-gray-300"><EMAIL></p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <i className="ri-time-fill text-[#013688]"></i>
                </div>
                <p className="text-gray-300">24/7 Customer Support</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <div className="bg-gray-800 py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">Stay Updated with Latest Deals</h3>
            <p className="text-gray-300">Subscribe to our newsletter and never miss out on exclusive offers and travel tips</p>
          </div>
          <div className="flex flex-col sm:flex-row max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 text-gray-900 bg-white rounded-l-lg sm:rounded-r-none rounded-r-lg focus:outline-none focus:ring-2 focus:ring-[#013688]"
            />
            <button className="px-8 py-3 bg-[#013688] hover:bg-[#012458] text-white font-semibold rounded-r-lg sm:rounded-l-none rounded-l-lg transition-colors whitespace-nowrap cursor-pointer">
              Subscribe
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-black py-6">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2024 TravelBook. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">Cookie Policy</a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors cursor-pointer">Refund Policy</a>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-400 text-sm">We Accept:</span>
              <div className="flex space-x-2">
                <div className="w-8 h-5 flex items-center justify-center bg-gray-700 rounded">
                  <i className="ri-visa-line text-xs text-white"></i>
                </div>
                <div className="w-8 h-5 flex items-center justify-center bg-gray-700 rounded">
                  <i className="ri-mastercard-line text-xs text-white"></i>
                </div>
                <div className="w-8 h-5 flex items-center justify-center bg-gray-700 rounded">
                  <i className="ri-paypal-line text-xs text-white"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}