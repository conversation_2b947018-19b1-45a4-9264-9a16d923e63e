# Multi-Provider Flight Search System

## 🎯 Overview

This system provides a robust, step-by-step flow for handling flight search results from multiple providers with proper boolean handling, deduplication, sorting, filtering, and pagination.

## 📋 Quick Reference

### Main Function
```typescript
processFlightList(
  flightList: Flight[],     // New flights from provider
  isMaster: boolean,        // false = first provider, true = subsequent
  currentMasterState?,      // Existing master state (required if isMaster = true)
  activeFilters?,           // User-selected filters
  currentPage = 1,          // Page number
  pageSize = 20            // Flights per page
)
```

### Key Boolean Logic
- **`isMaster = false`**: Master list is empty (first provider)
  - ✅ Apply filters and pagination immediately
  - ✅ Return ready-to-display results
  
- **`isMaster = true`**: Master list has existing data (subsequent providers)
  - ✅ Merge, dedupe, and sort
  - ❌ **DO NOT** apply filters or pagination
  - ✅ Return `shouldApplyFilters = true`

## 🔄 Processing Flow

### Case A: First Provider (isMaster = false)
1. **Normalize** → Validate and clean flight data
2. **Sort** → Order by lowest price first
3. **Save** → Store in master list
4. **Build Filters** → Extract filter options (airlines, price range, etc.)
5. **Apply Filters** → Filter based on user selections
6. **Paginate** → Split into pages (20 per batch)
7. **Display** → Return first batch for immediate display

### Case B: Subsequent Providers (isMaster = true)
1. **Normalize** → Validate and clean flight data
2. **Merge** → Combine with existing master list
3. **Dedupe** → Remove duplicate flights
4. **Sort** → Re-sort entire list by price
5. **Update Filters** → Refresh filter options with new data
6. **STOP** → Return updated master state only

## 🛠️ Helper Functions

| Function | Purpose | Input | Output |
|----------|---------|-------|--------|
| `normalizeFlights()` | Clean & validate data | `Flight[]` | `Flight[]` |
| `dedupeFlights()` | Remove duplicates | `Flight[]` | `Flight[]` |
| `sortFlightsByPrice()` | Sort by price + duration | `Flight[]` | `Flight[]` |
| `buildFilterOptions()` | Extract filter options | `Flight[]` | `FlightFilterOptions` |
| `applyFilters()` | Apply user filters | `Flight[], ActiveFilters` | `Flight[]` |
| `paginateFlights()` | Split into pages | `Flight[], page, size` | `PaginatedFlights` |

## 🎨 TypeScript Interfaces

```typescript
// Main interfaces
interface MasterFlightListState {
  flights: Flight[];
  filterOptions: FlightFilterOptions;
  totalFlights: number;
  lastUpdated: Date;
  providers: string[];
}

interface ActiveFilters {
  airlines: string[];
  priceRange: [number, number];
  departure: string[];
  arrival: string[];
  stops: string[];
  refundableOnly: boolean;
}

interface PaginatedFlights {
  flights: Flight[];
  currentPage: number;
  totalPages: number;
  totalFlights: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
```

## 🚀 Usage Examples

### First Provider
```typescript
const result = processFlightList(
  amadeusFlights,
  false,              // First provider
  undefined,          // No existing state
  userFilters,        // Apply filters
  1,                  // Page 1
  20                  // 20 per page
);

// Display result.paginatedResult.flights immediately
console.log(`Showing ${result.paginatedResult.flights.length} flights`);
```

### Subsequent Provider
```typescript
const result = processFlightList(
  sabreFlights,
  true,               // Subsequent provider
  existingMasterState, // Pass existing state
  undefined,          // Don't apply filters here
  1,
  20
);

// Check if filters need reapplication
if (result.shouldApplyFilters) {
  const filtered = applyFilters(result.masterState.flights, userFilters);
  const paginated = paginateFlights(filtered, 1, 20);
  // Display paginated.flights
}
```

## 🧪 Edge Cases Handled

- ✅ **Empty flight lists** → Returns valid empty state
- ✅ **Invalid flight data** → Normalizes with fallbacks
- ✅ **Missing prices** → Sets to 0, excludes from calculations
- ✅ **Duplicate flights** → Comprehensive deduplication
- ✅ **Network failures** → Graceful error handling
- ✅ **Malformed dates** → Fallback to current date
- ✅ **Missing required fields** → Applies sensible defaults

## 📊 Deduplication Strategy

Uses composite key matching:
```
airline-flightNumber-departureTime-date-arrivalTime
```

Plus ID-based deduplication as backup. Logs all removed duplicates for debugging.

## 🎛️ Filter Options Auto-Generation

Automatically extracts from flight data:
- **Airlines**: Unique airline codes
- **Price Range**: Actual min/max from flight prices
- **Duration Range**: Actual min/max flight durations
- **Stops**: Available stop options (0, 1, 2+)
- **Time Slots**: Morning, Afternoon, Evening, Night

## 📄 Pagination Details

- **Default**: 20 flights per page
- **Configurable**: Adjustable page size
- **Metadata**: Includes total pages, current page, navigation flags
- **Edge Cases**: Handles empty lists, invalid page numbers

## 🔍 Sorting Logic

**Primary**: Price (lowest first)
**Secondary**: Duration (shortest first)

Ensures consistent ordering across all providers and maintains user expectations.

## 📁 File Structure

```
app/api/services/flightService.ts    # Main implementation
docs/flight-search-flow.md           # Detailed documentation
examples/flight-search-example.ts    # Working examples
README-flight-search-system.md       # This summary
```

## 🎯 Integration Notes

1. **React State**: Works with existing state management
2. **Filter Updates**: Automatically refreshes filter options
3. **Pagination Reset**: Should reset to page 1 when new providers added
4. **UI Responsibility**: UI handles `shouldApplyFilters` flag
5. **Error Handling**: Graceful fallbacks maintain app stability

## 🚦 Best Practices

- Always check `shouldApplyFilters` flag after subsequent providers
- Reset pagination when master list updates
- Log deduplication statistics for monitoring
- Handle loading states during provider processing
- Validate flight data before processing
- Use TypeScript interfaces for type safety

## 🔧 Testing

Run the example file to see the complete flow in action:
```bash
npx ts-node examples/flight-search-example.ts
```

This demonstrates:
- ✅ First provider processing
- ✅ Subsequent provider merging
- ✅ Deduplication in action
- ✅ Filter reapplication
- ✅ Edge case handling

## 📈 Performance Considerations

- **Normalization**: O(n) per provider
- **Deduplication**: O(n) with Set-based lookup
- **Sorting**: O(n log n) per master list update
- **Filtering**: O(n) per filter application
- **Pagination**: O(1) slice operation

Optimized for real-world flight search volumes (typically 50-500 flights per provider).

---

**Ready to implement?** Start with the main `processFlightList()` function and follow the step-by-step flow documentation!
