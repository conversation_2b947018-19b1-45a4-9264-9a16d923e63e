import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// Backend API configuration
const BACKEND_API_BASE_URL = 'http://192.168.0.182:8080'; // Flight service backend
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance for backend API calls
const backendApi = axios.create({
  baseURL: BACKEND_API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tui = searchParams.get('tui');
    const from = searchParams.get('from');
    const to = searchParams.get('to');

    console.log('🚀 Preview API GET called with params:', { tui, from, to });

    // Validate required parameters
    if (!tui) {
      return NextResponse.json({
        Code: "400",
        Completed: false,
        Msg: ["TUI parameter is required"],
        TUI: null
      }, { status: 400 });
    }

    try {
      // Call backend API - adjust the endpoint path as needed
      const backendResponse = await backendApi.get(`/v1/preview/${tui}`, {
        params: {
          ...(from && { from }),
          ...(to && { to })
        }
      });

      console.log('✅ Backend API response received:', {
        status: backendResponse.status,
        hasData: !!backendResponse.data
      });

      // Return the backend response directly
      return NextResponse.json(backendResponse.data);

    } catch (backendError: any) {
      console.error('❌ Backend API error:', {
        message: backendError.message,
        status: backendError.response?.status,
        statusText: backendError.response?.statusText,
        data: backendError.response?.data
      });

      // Return mock data as fallback
      const mockApiData = {
        Code: "200",
        Completed: false,
        Msg: ["Preview pending - waiting for provider response"],
        TUI: tui || "ed7d014e-cb91-4489-b16a-b97f2d8f6939"
      };

      return NextResponse.json(mockApiData);
    }

  } catch (error) {
    console.error('❌ Preview API GET error:', error);

    return NextResponse.json({
      Code: "500",
      Completed: false,
      Msg: ["Internal server error"],
      TUI: null
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🚀 Preview API POST called with body:', body);

    // Extract required fields from request body
    const {
      ClientID,
      Mode,
      Options,
      Source,
      TripType,
      Provider,
      Trips,
      Amount,
      Index,
      OrderID,
      TUI,
      priceIds
    } = body;

    // Validate required fields
    if (!ClientID || !TUI) {
      return NextResponse.json({
        Code: "400",
        Completed: false,
        Msg: ["ClientID and TUI are required"],
        TUI: null
      }, { status: 400 });
    }

    try {
      // Call backend API for preview creation/processing
      const backendResponse = await backendApi.post('/v1/preview', body);

      console.log('✅ Backend API POST response received:', {
        status: backendResponse.status,
        hasData: !!backendResponse.data
      });

      // Return the backend response directly
      return NextResponse.json(backendResponse.data);

    } catch (backendError: any) {
      console.error('❌ Backend API POST error:', {
        message: backendError.message,
        status: backendError.response?.status,
        statusText: backendError.response?.statusText,
        data: backendError.response?.data
      });

      // Return mock response as fallback
      const mockResponse = {
        Code: "200",
        Completed: false,
        Msg: ["Preview pending - waiting for provider response"],
        TUI: TUI || "ed7d014e-cb91-4489-b16a-b97f2d8f6939"
      };

      return NextResponse.json(mockResponse);
    }

  } catch (error) {
    console.error('❌ Preview API POST error:', error);

    return NextResponse.json({
      Code: "500",
      Completed: false,
      Msg: ["Internal server error"],
      TUI: null
    }, { status: 500 });
  }
}
