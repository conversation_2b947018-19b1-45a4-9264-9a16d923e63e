/**
 * Complete Flight Search Flow Example
 * Demonstrates the correct step-by-step processing of multiple provider responses
 */

import {
  Flight,
  MasterFlightListState,
  ActiveFilters,
  processFlightList,
  normalizeFlights,
  dedupeFlights,
  sortFlightsByPrice,
  buildFilterOptions,
  applyFilters,
  paginateFlights
} from '../app/api/services/flightService';

// ============================================================================
// MOCK DATA FOR DEMONSTRATION
// ============================================================================

const createMockFlight = (
  id: string,
  airline: string,
  flightNumber: string,
  price: number,
  departureTime: string,
  duration: string,
  stops: string = '0'
): Flight => ({
  id,
  airline,
  logo: `/images/${airline.toLowerCase()}-logo.png`,
  flightNumber,
  segments: [],
  departure: {
    time: departureTime,
    airport: 'DEL',
    city: 'Delhi',
    date: '2025-08-21'
  },
  arrival: {
    time: '10:00',
    airport: 'BOM',
    city: 'Mumbai',
    date: '2025-08-21'
  },
  duration,
  stops,
  price,
  currency: 'INR',
  baggage: { cabin: '7 kg', checkin: '15 kg' },
  cancellation: 'Non-refundable',
  rating: 4.2,
  features: ['Meal', 'Entertainment'],
  fares: [],
  isRefundable: false,
  fareType: 'Economy'
});

// Provider 1 flights (e.g., from Amadeus)
const provider1Flights: Flight[] = [
  createMockFlight('AI101', 'Air India', 'AI101', 4500, '06:00', '2h 15m'),
  createMockFlight('6E201', 'IndiGo', '6E201', 3800, '08:30', '2h 10m'),
  createMockFlight('SG301', 'SpiceJet', 'SG301', 3200, '14:15', '2h 20m'),
  createMockFlight('UK401', 'Vistara', 'UK401', 5200, '16:45', '2h 05m'),
];

// Provider 2 flights (e.g., from Sabre) - includes some duplicates and new flights
const provider2Flights: Flight[] = [
  createMockFlight('6E201-dup', 'IndiGo', '6E201', 3800, '08:30', '2h 10m'), // Duplicate
  createMockFlight('G8501', 'GoAir', 'G8501', 2900, '12:00', '2h 25m'), // New
  createMockFlight('AI501', 'Air India', 'AI501', 4800, '18:30', '2h 30m'), // New
  createMockFlight('UK401-dup', 'Vistara', 'UK401', 5200, '16:45', '2h 05m'), // Duplicate
];

// Provider 3 flights (e.g., from direct airline APIs)
const provider3Flights: Flight[] = [
  createMockFlight('AI701', 'Air India', 'AI701', 4200, '20:15', '2h 12m'),
  createMockFlight('6E801', 'IndiGo', '6E801', 3500, '22:00', '2h 08m'),
  createMockFlight('I5901', 'AirAsia', 'I5901', 2800, '05:30', '2h 35m'), // Cheapest
];

// ============================================================================
// DEMONSTRATION FUNCTIONS
// ============================================================================

/**
 * Demonstrates the complete flow with multiple providers
 */
export const demonstrateCompleteFlow = () => {
  console.log('🚀 Starting Multi-Provider Flight Search Flow Demonstration\n');

  // Active filters that user has selected
  const activeFilters: ActiveFilters = {
    airlines: [], // No airline filter
    priceRange: [0, 10000], // Price range filter
    departure: ['morning', 'afternoon'], // Time slot filter
    arrival: [],
    stops: ['0'], // Non-stop flights only
    refundableOnly: false
  };

  let masterState: MasterFlightListState | undefined;

  // ========================================================================
  // CASE A: FIRST PROVIDER (isMaster = false)
  // ========================================================================
  
  console.log('📋 CASE A: Processing First Provider (Amadeus)');
  console.log(`Incoming flights: ${provider1Flights.length}`);
  
  const firstProviderResult = processFlightList(
    provider1Flights,
    false, // isMaster = false (first provider)
    undefined, // No existing master state
    activeFilters,
    1, // Page 1
    20 // 20 per page
  );

  masterState = firstProviderResult.masterState;

  console.log('✅ First Provider Results:');
  console.log(`  - Master list flights: ${masterState.totalFlights}`);
  console.log(`  - Filtered flights: ${firstProviderResult.filteredFlights?.length}`);
  console.log(`  - Paginated flights: ${firstProviderResult.paginatedResult?.flights.length}`);
  console.log(`  - Available airlines: ${masterState.filterOptions.airlines.join(', ')}`);
  console.log(`  - Price range: ₹${masterState.filterOptions.priceRange.min} - ₹${masterState.filterOptions.priceRange.max}`);
  console.log(`  - Should apply filters: ${firstProviderResult.shouldApplyFilters}\n`);

  // ========================================================================
  // CASE B: SECOND PROVIDER (isMaster = true)
  // ========================================================================
  
  console.log('📋 CASE B: Processing Second Provider (Sabre)');
  console.log(`Incoming flights: ${provider2Flights.length}`);
  console.log(`Existing master flights: ${masterState.totalFlights}`);
  
  const secondProviderResult = processFlightList(
    provider2Flights,
    true, // isMaster = true (subsequent provider)
    masterState,
    undefined, // Don't apply filters in this step
    1,
    20
  );

  masterState = secondProviderResult.masterState;

  console.log('✅ Second Provider Results:');
  console.log(`  - Master list flights: ${masterState.totalFlights}`);
  console.log(`  - Available airlines: ${masterState.filterOptions.airlines.join(', ')}`);
  console.log(`  - Price range: ₹${masterState.filterOptions.priceRange.min} - ₹${masterState.filterOptions.priceRange.max}`);
  console.log(`  - Should apply filters: ${secondProviderResult.shouldApplyFilters}`);
  console.log(`  - Providers: ${masterState.providers.length}\n`);

  // UI should now reapply filters since shouldApplyFilters is true
  if (secondProviderResult.shouldApplyFilters) {
    console.log('🔄 UI Reapplying Filters After Second Provider:');
    const reappliedFiltered = applyFilters(masterState.flights, activeFilters);
    const reappliedPaginated = paginateFlights(reappliedFiltered, 1, 20);
    
    console.log(`  - Filtered flights: ${reappliedFiltered.length}`);
    console.log(`  - Paginated flights: ${reappliedPaginated.flights.length}`);
    console.log(`  - Total pages: ${reappliedPaginated.totalPages}\n`);
  }

  // ========================================================================
  // CASE B: THIRD PROVIDER (isMaster = true)
  // ========================================================================
  
  console.log('📋 CASE B: Processing Third Provider (Direct Airlines)');
  console.log(`Incoming flights: ${provider3Flights.length}`);
  console.log(`Existing master flights: ${masterState.totalFlights}`);
  
  const thirdProviderResult = processFlightList(
    provider3Flights,
    true, // isMaster = true (subsequent provider)
    masterState,
    undefined, // Don't apply filters in this step
    1,
    20
  );

  masterState = thirdProviderResult.masterState;

  console.log('✅ Third Provider Results:');
  console.log(`  - Master list flights: ${masterState.totalFlights}`);
  console.log(`  - Available airlines: ${masterState.filterOptions.airlines.join(', ')}`);
  console.log(`  - Price range: ₹${masterState.filterOptions.priceRange.min} - ₹${masterState.filterOptions.priceRange.max}`);
  console.log(`  - Should apply filters: ${thirdProviderResult.shouldApplyFilters}`);
  console.log(`  - Providers: ${masterState.providers.length}\n`);

  // Final filter application
  if (thirdProviderResult.shouldApplyFilters) {
    console.log('🎯 Final Filter Application:');
    const finalFiltered = applyFilters(masterState.flights, activeFilters);
    const finalPaginated = paginateFlights(finalFiltered, 1, 20);
    
    console.log(`  - Total flights in master: ${masterState.totalFlights}`);
    console.log(`  - Flights after filters: ${finalFiltered.length}`);
    console.log(`  - Flights on page 1: ${finalPaginated.flights.length}`);
    console.log(`  - Cheapest flight: ₹${finalPaginated.flights[0]?.price} (${finalPaginated.flights[0]?.airline} ${finalPaginated.flights[0]?.flightNumber})`);
  }

  return masterState;
};

/**
 * Demonstrates edge case handling
 */
export const demonstrateEdgeCases = () => {
  console.log('\n🧪 EDGE CASE DEMONSTRATIONS\n');

  // Empty flight list
  console.log('1. Empty Flight List:');
  const emptyResult = processFlightList([], false);
  console.log(`   Result: ${emptyResult.masterState.totalFlights} flights\n`);

  // Invalid flight data
  console.log('2. Invalid Flight Data:');
  const invalidFlights = [
    { id: 'invalid1', price: null, airline: 'Test' } as any,
    { id: 'invalid2', airline: null, price: 'not-a-number' } as any,
    {} as any // Completely empty object
  ];
  const invalidResult = processFlightList(invalidFlights, false);
  console.log(`   Normalized to: ${invalidResult.masterState.totalFlights} valid flights\n`);

  // Duplicate detection
  console.log('3. Duplicate Detection:');
  const duplicateFlights = [
    createMockFlight('dup1', 'TestAir', 'TA101', 5000, '10:00', '2h 00m'),
    createMockFlight('dup2', 'TestAir', 'TA101', 5000, '10:00', '2h 00m'), // Same flight
    createMockFlight('dup3', 'TestAir', 'TA102', 5500, '12:00', '2h 15m'),
  ];
  const dedupeResult = processFlightList(duplicateFlights, false);
  console.log(`   Input: ${duplicateFlights.length} flights → Output: ${dedupeResult.masterState.totalFlights} flights\n`);
};

/**
 * Demonstrates helper functions individually
 */
export const demonstrateHelperFunctions = () => {
  console.log('\n🔧 HELPER FUNCTION DEMONSTRATIONS\n');

  const testFlights = provider1Flights.slice(0, 3);

  // Normalization
  console.log('1. Flight Normalization:');
  const normalized = normalizeFlights(testFlights);
  console.log(`   Normalized ${normalized.length} flights successfully\n`);

  // Sorting
  console.log('2. Price Sorting:');
  const sorted = sortFlightsByPrice(testFlights);
  console.log('   Sorted by price:');
  sorted.forEach((flight, index) => {
    console.log(`   ${index + 1}. ${flight.airline} ${flight.flightNumber}: ₹${flight.price}`);
  });
  console.log();

  // Filter options building
  console.log('3. Filter Options Building:');
  const filterOptions = buildFilterOptions(testFlights);
  console.log(`   Airlines: ${filterOptions.airlines.join(', ')}`);
  console.log(`   Price Range: ₹${filterOptions.priceRange.min} - ₹${filterOptions.priceRange.max}`);
  console.log(`   Stops: ${filterOptions.stops.join(', ')}\n`);

  // Pagination
  console.log('4. Pagination:');
  const paginated = paginateFlights(testFlights, 1, 2);
  console.log(`   Page ${paginated.currentPage}/${paginated.totalPages}`);
  console.log(`   Showing ${paginated.flights.length} of ${paginated.totalFlights} flights`);
  console.log(`   Has next page: ${paginated.hasNextPage}\n`);
};

// Run demonstrations
if (require.main === module) {
  demonstrateCompleteFlow();
  demonstrateEdgeCases();
  demonstrateHelperFunctions();
}
