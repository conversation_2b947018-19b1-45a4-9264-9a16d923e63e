'use client';

import React, { useState } from 'react';
import { FlightOptionInternational } from '@/app/models/flight-list.model';
import { Clock, Plane, Users, Luggage, Wifi, Utensils, Monitor, ChevronDown, ChevronUp, Globe } from 'lucide-react';

interface RoundTripInternationalCardProps {
  flight: FlightOptionInternational;
  journeyType: 'onward' | 'return';
  onSelect?: (flight: FlightOptionInternational) => void;
}

export default function RoundTripInternationalCard({ flight, journeyType, onSelect }: RoundTripInternationalCardProps) {
  const [showDetails, setShowDetails] = useState(false);

  // Extract flight data from the API structure
  const segments = flight.sI || [];
  const firstSegment = segments[0] || {};
  const lastSegment = segments[segments.length - 1] || {};
  const fareInfo = flight.totalPriceList?.[0] || {};

  // Calculate total duration
  const totalDuration = segments.reduce((total: number, segment: any) => {
    const duration = segment.duration || 0;
    return total + duration;
  }, 0);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (dateTime: string) => {
    if (!dateTime) return '--:--';
    try {
      const date = new Date(dateTime);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch {
      return '--:--';
    }
  };

  const formatDate = (dateTime: string) => {
    if (!dateTime) return '';
    try {
      const date = new Date(dateTime);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return '';
    }
  };

  const getAirlineLogo = (airlineCode: string) => {
    return `https://via.placeholder.com/40x40/0066CC/FFFFFF?text=${airlineCode || 'XX'}`;
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.src = 'https://via.placeholder.com/40x40/0066CC/FFFFFF?text=XX';
  };

  const journeyBadgeColor = journeyType === 'onward'
    ? 'bg-blue-100 text-blue-800'
    : 'bg-green-100 text-green-800';

  const isMultiSegment = segments.length > 1;

  // Get fare information
  const adultFare = fareInfo.fd?.ADULT;
  const totalFare = adultFare?.fC?.TF || 0;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
      {/* Journey Type Badge and International Indicator */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${journeyBadgeColor}`}>
            {journeyType === 'onward' ? 'Onward Journey' : 'Return Journey'}
          </span>
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 flex items-center space-x-1">
            <Globe className="w-3 h-3" />
            <span>International</span>
          </span>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">
            ₹{totalFare.toLocaleString()}
          </div>
          <div className="text-sm text-gray-500">per person</div>
        </div>
      </div>

      {/* Airline Info Header - Match Old FlightCard */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <img
              src={getAirlineLogo(firstSegment.fD?.aI?.code)}
              alt={firstSegment.fD?.aI?.name || 'Airline'}
              className="w-10 h-6 object-contain"
              onError={handleImageError}
            />
          </div>
          <div>
            <div className="font-semibold text-gray-900 text-sm">
              {firstSegment.fD?.aI?.name || 'Unknown Airline'}
            </div>
            <div className="text-gray-500 text-xs">
              {isMultiSegment ? 'Multiple Airlines' : firstSegment.fD?.fN || 'N/A'}
            </div>
          </div>
        </div>
      </div>

      {/* Flight Times and Duration - Match Old FlightCard 5-column Grid */}
      <div className="grid grid-cols-5 gap-2 items-center mb-3">
        {/* Departure */}
        <div className="text-left">
          <div className="font-bold text-gray-900 text-xl">{formatTime(firstSegment.dt)}</div>
          <div className="text-gray-600 text-sm">{firstSegment.da?.city}</div>
          <div className="text-gray-500 text-xs">{firstSegment.da?.code}</div>
        </div>

        {/* Duration and Route */}
        <div className="col-span-3 text-center">
          <div className="text-gray-600 text-sm mb-1">{formatDuration(totalDuration)}</div>
          <div className="flex items-center justify-center mb-1">
            <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
            <div className="flex-1 h-px bg-gray-300 mx-2 relative">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <i className="ri-plane-line text-[#013688] text-sm"></i>
              </div>
            </div>
            <div className="w-2 h-2 bg-[#013688] rounded-full"></div>
          </div>
          <div className="text-gray-500 text-xs">
            {isMultiSegment ? `${segments.length - 1} stop${segments.length > 2 ? 's' : ''}` : 'Non-stop'}
          </div>
        </div>

        {/* Arrival */}
        <div className="text-right">
          <div className="font-bold text-gray-900 text-xl">{formatTime(lastSegment.at)}</div>
          <div className="text-gray-600 text-sm">{lastSegment.aa?.city}</div>
          <div className="text-gray-500 text-xs">{lastSegment.aa?.code}</div>
        </div>
      </div>

      {/* Price and Action - Match Old FlightCard Bottom Section */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="flex items-center space-x-4">
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-bold text-gray-900 text-xl">₹{totalFare.toLocaleString()}</span>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <i className="ri-luggage-cart-line text-xs"></i>
              <span>30 kg</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-[#013688] font-semibold hover:text-blue-700 transition-colors text-sm"
          >
            {showDetails ? 'Hide Details' : 'Flight Details'}
          </button>
          <button
            onClick={() => onSelect?.(flight)}
            className="bg-[#013688] text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
          >
            Select
          </button>
        </div>
      </div>

      {/* Expandable Details */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="font-semibold mb-2">Flight Details</h4>
          {segments.map((segment: any, index: number) => (
            <div key={index} className="mb-3 last:mb-0">
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium">
                    {segment.da?.city} → {segment.aa?.city}
                  </div>
                  <div className="text-sm text-gray-600">
                    {segment.fD?.aI?.name} • {segment.fD?.fN}
                  </div>
                  <div className="text-xs text-gray-500">
                    Aircraft: {segment.fD?.eT || 'N/A'}
                  </div>
                </div>
                <div className="text-right text-sm">
                  <div>{formatTime(segment.dt)} - {formatTime(segment.at)}</div>
                  <div className="text-gray-500">{formatDuration(segment.duration || 0)}</div>
                  <div className="text-xs text-gray-500">
                    {formatDate(segment.dt)} - {formatDate(segment.at)}
                  </div>
                </div>
              </div>
              {index < segments.length - 1 && (
                <div className="mt-2 text-xs text-orange-600">
                  Layover at {segment.aa?.city}
                </div>
              )}
            </div>
          ))}

          {/* International Fare Rules */}
          <div className="mt-3 pt-3 border-t border-gray-100">
            <h5 className="font-medium text-sm mb-2">International Fare Rules</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Cancellation charges apply as per airline policy</li>
              <li>• Date change allowed with charges</li>
              <li>• No refund on no-show</li>
              <li>• Baggage allowance: 30kg check-in + 7kg cabin</li>
              <li>• Visa and passport required for international travel</li>
              <li>• Check-in closes 3 hours before departure</li>
            </ul>
          </div>
        </div>
      )}
    </div> 
  );
}