import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import axiosInstance from './axiosinstance';

class ApiService {
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async put<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async postFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async putFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  // 👇 Safe, typed error handling
  private handleError(error: unknown): never {
    if (error instanceof AxiosError) {
      console.error('API Error:', error.response?.data || error.message);
      throw error;
    }

    // Unknown type fallback
    console.error('Unexpected error:', error);
    throw new Error('An unexpected error occurred');
  }
}

const apiService = new ApiService();
export default apiService;
