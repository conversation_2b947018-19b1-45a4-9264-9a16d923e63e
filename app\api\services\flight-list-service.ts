import { FlightSearchPayload } from "@/app/models/flight-search.model";
import apiService from "./api-service";
import {
  FlightApiResponseInternational,
  FlightApiResponseOneWay,
  FlightApiResponseRoundTripDomestic
} from "@/app/models/flight-list.model";
import { AirportSelectorData } from "./airportService";

/**
 * Service for handling flight search API calls
 * Provides specific functions for different flight types
 */

/**
 * Form data interface for flight search
 */
export interface FlightSearchFormData {
  from: AirportSelectorData;
  to: AirportSelectorData;
  departureDate: string;
  returnDate?: string;
  adults: number;
  children: number;
  infants: number;
  cabin: 'E' | 'PE' | 'B' | 'F';
  tripType: 'oneWay' | 'roundTrip';
  fareType: 'ON' | 'OFF';
  isDirect: boolean;
}

/**
 * Helper function to determine if a route is international
 */
export const isInternationalRoute = (fromAirport: AirportSelectorData, toAirport: AirportSelectorData): boolean => {
  // Check if both airports are in the same country
  return fromAirport.country !== toAirport.country;
};

/**
 * Transform form data to API payload
 */
export const transformFormDataToPayload = (formData: FlightSearchFormData): FlightSearchPayload => {
  const trips = [{
    From: formData.from.code,
    To: formData.to.code,
    OnwardDate: formData.departureDate,
    ReturnDate: formData.returnDate || '',
    TUI: 'TUI_' + Date.now()
  }];

  return {
    tui: 'TUI_' + Date.now(),
    search: {
      ADT: formData.adults,
      CHD: formData.children,
      INF: formData.infants,
      Cabin: formData.cabin,
      Source: 'WEB',
      Mode: 'LIVE',
      ClientID: 'ECOGO_CLIENT',
      FareType: formData.fareType,
      SecType: 'NORMAL',
      TUI: 'TUI_' + Date.now(),
      Trips: trips,
      Parameters: {
        IsDirect: formData.isDirect,
        PaxCategory: 'NORMAL',
        Refundable: 'ALL'
      }
    }
  };
};

/**
 * Search for one-way flights
 * @param body - Flight search payload
 * @returns Promise with one-way flight response
 */
export const getFlightListOneWay = async (body: FlightSearchPayload): Promise<FlightApiResponseOneWay> => {
  try {
    console.log('🔍 Searching one-way flights with payload:', body);
    const response = await apiService.post<FlightApiResponseOneWay>('search', body);

    if (!response) {
      throw new Error('No response received from one-way flight search API');
    }

    console.log('✅ One-way flight search successful:', {
      status: response.status,
      provider: response.provider,
      tui: response.tui,
      isComplete: response.searchResult?.isComplete,
      flightCount: response.searchResult?.searchResult?.tripInfos?.ONWARD?.length || 0
    });

    return response;
  } catch (error) {
    console.error('❌ Error in one-way flight search:', error);
    throw new Error(`One-way flight search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Search for round-trip international flights
 * @param body - Flight search payload
 * @returns Promise with international flight response
 */
export const getFlightListRoundTripInternational = async (body: FlightSearchPayload): Promise<FlightApiResponseInternational> => {
  try {
    console.log('🔍 Searching round-trip international flights with payload:', body);
    const response = await apiService.post<FlightApiResponseInternational>('search', body);

    if (!response) {
      throw new Error('No response received from international flight search API');
    }

    console.log('✅ International flight search successful:', {
      success: response.status?.success,
      httpStatus: response.status?.httpStatus,
      flightCount: response.searchResult?.tripInfos?.COMBO?.length || 0
    });

    return response;
  } catch (error) {
    console.error('❌ Error in international flight search:', error);
    throw new Error(`International flight search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Search for round-trip domestic flights
 * @param body - Flight search payload
 * @returns Promise with domestic flight response
 */
export const getFlightListRoundTripDomestic = async (body: FlightSearchPayload): Promise<FlightApiResponseRoundTripDomestic> => {
  try {
    console.log('🔍 Searching round-trip domestic flights with payload:', body);
    const response = await apiService.post<FlightApiResponseRoundTripDomestic>('search', body);

    if (!response) {
      throw new Error('No response received from domestic flight search API');
    }

    console.log('✅ Domestic flight search successful:', {
      success: response.status?.success,
      httpStatus: response.status?.httpStatus,
      flightCount: response.searchResult?.tripInfos?.RETURN?.length || 0
    });

    return response;
  } catch (error) {
    console.error('❌ Error in domestic flight search:', error);
    throw new Error(`Domestic flight search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Helper function to determine flight type and call appropriate service
 * @param body - Flight search payload
 * @param isInternational - Whether the route is international
 * @returns Promise with appropriate flight response
 */
export const searchFlights = async (
  body: FlightSearchPayload,
  isInternational: boolean = false
): Promise<FlightApiResponseOneWay | FlightApiResponseInternational | FlightApiResponseRoundTripDomestic> => {
  const isRoundTrip = body.search.Trips.length > 1 ||
                     (body.search.Trips[0]?.ReturnDate && body.search.Trips[0].ReturnDate !== '');

  if (!isRoundTrip) {
    return getFlightListOneWay(body);
  } else if (isInternational) {
    return getFlightListRoundTripInternational(body);
  } else {
    return getFlightListRoundTripDomestic(body);
  }
};

/**
 * Main search function that accepts form data and calls appropriate service
 * @param formData - Flight search form data
 * @returns Promise with appropriate flight response and metadata
 */
export const searchFlightsWithFormData = async (formData: FlightSearchFormData) => {
  try {
    console.log('🔍 Starting flight search with form data:', formData);

    // Transform form data to API payload
    const payload = transformFormDataToPayload(formData);
    console.log('📦 Transformed payload:', payload);

    // Determine if route is international
    const isInternational = isInternationalRoute(formData.from, formData.to);
    console.log('🌍 Route type:', isInternational ? 'International' : 'Domestic');

    // Call appropriate service based on trip type and route
    const response = await searchFlights(payload, isInternational);

    return {
      success: true,
      data: response,
      isInternational,
      tripType: formData.tripType,
      message: 'Flight search completed successfully'
    };
  } catch (error) {
    console.error('❌ Flight search failed:', error);
    return {
      success: false,
      data: null,
      isInternational: false,
      tripType: formData.tripType,
      message: error instanceof Error ? error.message : 'Flight search failed',
      error
    };
  }
};

/**
 * Parse passenger counts from URL search params
 */
export const parsePassengerCounts = (searchParams: URLSearchParams) => {
  const travelers = searchParams.get('travelers') || '1-0-0';
  const [adults = '1', children = '0', infants = '0'] = travelers.split('-');

  return {
    adults: parseInt(adults, 10) || 1,
    children: parseInt(children, 10) || 0,
    infants: parseInt(infants, 10) || 0
  };
};

// Legacy function name for backward compatibility
export const getFlightListRoundTripinternational = getFlightListRoundTripInternational;