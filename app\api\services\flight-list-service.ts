import { FlightSearchPayload } from "@/app/models/flight-search.model";
import apiService from "./api-service";
import { FlightApiResponseInternational, FlightApiResponseOneWay, FlightApiResponseRoundTripDomestic } from "@/app/models/flight-list.model";

export const getFlightListOneWay = async (body:FlightSearchPayload) => {
    const response = await apiService.post<FlightApiResponseOneWay>('search',body)
    return response
}

export const getFlightListRoundTripinternational = async (body:FlightSearchPayload) => {
    const response = await apiService.post<FlightApiResponseInternational>('search',body)
    return response
}

export const getFlightListRoundTripDomestic = async (body:FlightSearchPayload) => {
    const response = await apiService.post<FlightApiResponseRoundTripDomestic>('search',body)
    return response
}